# Read about fixtures at https://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

# == Schema Information
#
# Table name: users(用户表 - 存储用户基本信息和认证数据)
#
#  id                                                                                              :bigint           not null, primary key
#  birthday(生日)                                                                                  :date
#  email(邮箱地址，用于登录和验证)                                                                 :string(255)      not null
#  email_verified(邮箱是否已验证)                                                                  :boolean          default(FALSE), not null
#  email_verified_at(邮箱验证时间)                                                                 :datetime
#  exercise_frequency(运动频率：0-偶尔锻炼(0-2次/周)，1-定期锻炼(3-5次/周)，2-专业运动员(6+次/周)) :integer
#  fitness_goals(用户健身目标(JSON数组))                                                           :text
#  gender(性别：0-未知，1-男，2-女)                                                                :integer          default("unknown")
#  height(身高(cm))                                                                                :decimal(5, 2)
#  last_login_at(最后登录时间)                                                                     :datetime
#  last_login_ip(最后登录IP地址)                                                                   :string
#  name(用户姓名)                                                                                  :string(100)      not null
#  obstacles(阻碍用户实现目标的因素(JSON数组))                                                     :text
#  password_digest(加密后的密码)                                                                   :string           not null
#  password_reset_sent_at(密码重置令牌发送时间)                                                    :datetime
#  password_reset_token(密码重置令牌)                                                              :string
#  roles(用户角色数组：支持多个角色如['user', 'admin'])                                            :string           default(["user"]), not null, is an Array
#  status(用户状态：0-待验证，1-已激活，2-已禁用)                                                  :integer          default("inactive"), not null
#  weight(体重(kg))                                                                                :decimal(5, 2)
#  created_at(created_at: 创建时间, updated_at: 更新时间)                                          :datetime         not null
#  updated_at(created_at: 创建时间, updated_at: 更新时间)                                          :datetime         not null
#
# Indexes
#
#  index_users_on_birthday              (birthday)
#  index_users_on_email                 (email) UNIQUE
#  index_users_on_email_verified        (email_verified)
#  index_users_on_exercise_frequency    (exercise_frequency)
#  index_users_on_gender                (gender)
#  index_users_on_last_login_at         (last_login_at)
#  index_users_on_password_reset_token  (password_reset_token) UNIQUE
#  index_users_on_roles                 (roles) USING gin
#  index_users_on_roles_and_status      (roles,status)
#
one:
  email: MyString
  password_digest: MyString
  first_name: MyString
  last_name: MyString
  role: 1
  status: 1
  last_login_at: 2025-08-17 13:22:40
  last_login_ip: MyString
  email_verified_at: 2025-08-17 13:22:40
  reset_password_token: MyString
  reset_password_sent_at: 2025-08-17 13:22:40

two:
  email: MyString
  password_digest: MyString
  first_name: MyString
  last_name: MyString
  role: 1
  status: 1
  last_login_at: 2025-08-17 13:22:40
  last_login_ip: MyString
  email_verified_at: 2025-08-17 13:22:40
  reset_password_token: MyString
  reset_password_sent_at: 2025-08-17 13:22:40
