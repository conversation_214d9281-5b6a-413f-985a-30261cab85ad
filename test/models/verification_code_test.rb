# == Schema Information
#
# Table name: verification_codes(验证码表 - 管理邮箱验证和密码重置验证码)
#
#  id                                                     :bigint           not null, primary key
#  attempts(尝试次数)                                     :integer          default(0), not null
#  code(验证码)                                           :string(10)       not null
#  code_type(验证码类型：0-邮箱验证，1-密码重置)          :integer          not null
#  email(验证码发送的邮箱地址)                            :string(255)      not null
#  expires_at(验证码过期时间)                             :datetime         not null
#  used(是否已使用)                                       :boolean          default(FALSE), not null
#  used_at(使用时间)                                      :datetime
#  created_at(created_at: 创建时间, updated_at: 更新时间) :datetime         not null
#  updated_at(created_at: 创建时间, updated_at: 更新时间) :datetime         not null
#
# Indexes
#
#  index_verification_codes_on_code_and_code_type            (code,code_type)
#  index_verification_codes_on_email_and_code_type_and_used  (email,code_type,used)
#  index_verification_codes_on_email_and_expires_at          (email,expires_at)
#  index_verification_codes_on_expires_at                    (expires_at)
#
require "test_helper"

class VerificationCodeTest < ActiveSupport::TestCase
  # test "the truth" do
  #   assert true
  # end
end
