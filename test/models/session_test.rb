# == Schema Information
#
# Table name: sessions(会话表 - 管理用户登录会话和API认证)
#
#  id                                                     :bigint           not null, primary key
#  expires_at(会话过期时间)                               :datetime         not null
#  ip_address(登录IP地址)                                 :string
#  revoked(是否已撤销)                                    :boolean          default(FALSE), not null
#  revoked_at(撤销时间)                                   :datetime
#  token(会话令牌，用于API认证)                           :string(255)      not null
#  user_agent(用户代理信息)                               :string
#  created_at(created_at: 创建时间, updated_at: 更新时间) :datetime         not null
#  updated_at(created_at: 创建时间, updated_at: 更新时间) :datetime         not null
#  user_id(关联用户ID)                                    :bigint           not null
#
# Indexes
#
#  index_sessions_on_expires_at              (expires_at)
#  index_sessions_on_revoked_and_expires_at  (revoked,expires_at)
#  index_sessions_on_token                   (token) UNIQUE
#  index_sessions_on_user_id                 (user_id)
#  index_sessions_on_user_id_and_revoked     (user_id,revoked)
#
# Foreign Keys
#
#  fk_rails_...  (user_id => users.id)
#
require "test_helper"

class SessionTest < ActiveSupport::TestCase
  # test "the truth" do
  #   assert true
  # end
end
