<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Moonshot AI聊天WebSocket测试页面 - 新版API</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
            font-size: 16px;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 2fr;
        }

        .control-panel {
            padding: 20px;
            background: #f8f9fa;
            border-right: 1px solid #e9ecef;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .section {
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .section h3 {
            color: #495057;
            margin-bottom: 15px;
            font-size: 16px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 5px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #495057;
        }

        .form-control {
            width: 100%;
            padding: 10px;
            border: 1px solid #ced4da;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .form-control:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s;
            text-align: center;
            display: inline-block;
            min-width: 100px;
            margin: 2px;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background: #0056b3;
            transform: translateY(-1px);
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-success:hover {
            background: #1e7e34;
            transform: translateY(-1px);
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn-danger:hover {
            background: #c82333;
            transform: translateY(-1px);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .status {
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
            text-align: center;
            margin: 5px 0;
        }

        .status.connected {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.disconnected {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.connecting {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .chat-area {
            display: flex;
            flex-direction: column;
            background: white;
        }

        .messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            gap: 15px;
            height: 500px;
        }

        .message {
            max-width: 80%;
            padding: 12px 16px;
            border-radius: 18px;
            word-wrap: break-word;
            position: relative;
        }

        .message.user {
            align-self: flex-end;
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
        }

        .message.ai {
            align-self: flex-start;
            background: #f8f9fa;
            color: #495057;
            border: 1px solid #e9ecef;
        }

        .message .meta {
            font-size: 11px;
            opacity: 0.7;
            margin-top: 5px;
        }

        .message-input {
            padding: 20px;
            border-top: 1px solid #e9ecef;
            background: #f8f9fa;
        }

        .input-group {
            display: flex;
            gap: 10px;
        }

        .input-group .form-control {
            flex: 1;
        }

        .room-info {
            background: #e7f3ff;
            padding: 10px;
            border-radius: 6px;
            font-size: 12px;
            color: #0066cc;
        }

        .logs {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 11px;
            color: #495057;
            max-height: 150px;
            overflow-y: auto;
            border: 1px solid #e9ecef;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .typing {
            animation: pulse 1.5s infinite;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 6px;
            border: 1px solid #f5c6cb;
            font-size: 12px;
        }

        .success {
            background: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 6px;
            border: 1px solid #c3e6cb;
            font-size: 12px;
        }

        .api-response {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 10px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 11px;
            max-height: 120px;
            overflow-y: auto;
        }
    </style>
    <script src="https://cdn.jsdelivr.net/npm/axios@1.6.0/dist/axios.min.js"></script>
    <script src="https://unpkg.com/@rails/actioncable@^7.0.0/app/assets/javascripts/actioncable.js"></script>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Moonshot AI聊天WebSocket测试 (新版API)</h1>
            <p>支持统一API响应格式 | 测试用户：<EMAIL> | 密码：user123456</p>
            <div id="apiStatus" class="status disconnected">API状态检查中...</div>
        </div>
        
        <div class="main-content">
            <!-- 控制面板 -->
            <div class="control-panel">
                <!-- 认证状态 -->
                <div class="section">
                    <h3>🔐 认证状态</h3>
                    <div id="authStatus" class="status disconnected">未登录</div>
                    <div class="form-group">
                        <label>邮箱:</label>
                        <input type="email" id="emailInput" class="form-control" value="<EMAIL>">
                    </div>
                    <div class="form-group">
                        <label>密码:</label>
                        <input type="password" id="passwordInput" class="form-control" value="user123456">
                    </div>
                    <button id="loginBtn" class="btn btn-primary">登录</button>
                    <button id="logoutBtn" class="btn btn-danger" disabled>登出</button>
                </div>

                <!-- 聊天室管理 -->
                <div class="section">
                    <h3>💬 聊天室</h3>
                    <div class="form-group">
                        <label>聊天室名称:</label>
                        <input type="text" id="roomNameInput" class="form-control" value="API测试聊天室">
                    </div>
                    <button id="createRoomBtn" class="btn btn-success" disabled>创建聊天室</button>
                    <div id="roomInfo" class="room-info" style="display: none;"></div>
                </div>

                <!-- WebSocket状态 -->
                <div class="section">
                    <h3>🔌 WebSocket连接</h3>
                    <div id="wsStatus" class="status disconnected">未连接</div>
                    <button id="connectBtn" class="btn btn-primary" disabled>连接</button>
                    <button id="disconnectBtn" class="btn btn-danger" disabled>断开</button>
                </div>

                <!-- API测试 -->
                <div class="section">
                    <h3>🧪 API测试</h3>
                    <button id="testApiBtn" class="btn btn-primary">测试API连通性</button>
                    <button id="getUserInfoBtn" class="btn btn-primary" disabled>获取用户信息</button>
                    <button id="getModelsBtn" class="btn btn-primary">获取AI模型列表</button>
                    <div id="apiResponse" class="api-response" style="display: none;"></div>
                </div>

                <!-- 系统日志 -->
                <div class="section">
                    <h3>📋 系统日志</h3>
                    <div id="logs" class="logs"></div>
                    <button id="clearLogsBtn" class="btn btn-primary" style="margin-top: 10px;">清空日志</button>
                </div>
            </div>

            <!-- 聊天区域 -->
            <div class="chat-area">
                <div id="messages" class="messages">
                    <div class="message ai">
                        <div>你好！我是Kimi智能助手。本页面已更新支持新的统一API响应格式。请先登录并创建聊天室，然后连接WebSocket开始聊天。</div>
                        <div class="meta">系统消息 | 新版API支持</div>
                    </div>
                </div>
                
                <div class="message-input">
                    <div class="input-group">
                        <input type="text" id="messageInput" class="form-control" placeholder="输入消息..." disabled>
                        <button id="sendBtn" class="btn btn-primary" disabled>发送</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        class AIWebSocketTester {
            constructor() {
                this.apiBase = window.location.origin;
                this.authToken = null;
                this.currentUser = null;
                this.currentRoom = null;
                this.cable = null;
                this.subscription = null;
                this.isConnected = false;
                
                this.initializeElements();
                this.bindEvents();
                this.checkApiStatus();
                this.log('系统初始化完成 - 支持新版API响应格式');
            }

            initializeElements() {
                // 认证相关
                this.authStatus = document.getElementById('authStatus');
                this.emailInput = document.getElementById('emailInput');
                this.passwordInput = document.getElementById('passwordInput');
                this.loginBtn = document.getElementById('loginBtn');
                this.logoutBtn = document.getElementById('logoutBtn');

                // 聊天室相关
                this.roomNameInput = document.getElementById('roomNameInput');
                this.createRoomBtn = document.getElementById('createRoomBtn');
                this.roomInfo = document.getElementById('roomInfo');

                // WebSocket相关
                this.wsStatus = document.getElementById('wsStatus');
                this.connectBtn = document.getElementById('connectBtn');
                this.disconnectBtn = document.getElementById('disconnectBtn');

                // 聊天相关
                this.messages = document.getElementById('messages');
                this.messageInput = document.getElementById('messageInput');
                this.sendBtn = document.getElementById('sendBtn');

                // 日志和API测试
                this.logs = document.getElementById('logs');
                this.clearLogsBtn = document.getElementById('clearLogsBtn');
                this.apiStatus = document.getElementById('apiStatus');
                this.testApiBtn = document.getElementById('testApiBtn');
                this.getUserInfoBtn = document.getElementById('getUserInfoBtn');
                this.getModelsBtn = document.getElementById('getModelsBtn');
                this.apiResponse = document.getElementById('apiResponse');
            }

            bindEvents() {
                this.loginBtn.addEventListener('click', () => this.login());
                this.logoutBtn.addEventListener('click', () => this.logout());
                this.createRoomBtn.addEventListener('click', () => this.createRoom());
                this.connectBtn.addEventListener('click', () => this.connectWebSocket());
                this.disconnectBtn.addEventListener('click', () => this.disconnectWebSocket());
                this.sendBtn.addEventListener('click', () => this.sendMessage());
                this.clearLogsBtn.addEventListener('click', () => this.clearLogs());

                // API测试事件
                this.testApiBtn.addEventListener('click', () => this.testApiConnectivity());
                this.getUserInfoBtn.addEventListener('click', () => this.getUserInfo());
                this.getModelsBtn.addEventListener('click', () => this.getModels());

                // 回车发送消息
                this.messageInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        this.sendMessage();
                    }
                });

                // 回车登录
                this.passwordInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        this.login();
                    }
                });
            }

            // 通用API响应处理方法
            handleApiResponse(data, response, operation = '操作') {
                const responseStr = JSON.stringify(data, null, 2);
                this.log(`API响应 (${operation}): ${responseStr.substring(0, 300)}${responseStr.length > 300 ? '...' : ''}`);
                
                // 显示完整响应
                this.showApiResponse(data);
                
                if (data.success) {
                    if (data.message) {
                        this.showSuccess(data.message);
                    }
                    return { success: true, data: data.data || data };
                } else {
                    const errorMsg = data.message || '未知错误';
                    this.showError(`${operation}失败: ${errorMsg}`);
                    
                    if (data.details) {
                        this.log(`详细错误信息: ${JSON.stringify(data.details)}`);
                    }
                    
                    return { success: false, error: errorMsg, details: data.details };
                }
            }

            showApiResponse(data) {
                this.apiResponse.style.display = 'block';
                this.apiResponse.textContent = JSON.stringify(data, null, 2);
            }

            // 检查API状态
            async checkApiStatus() {
                try {
                    // 先尝试健康检查端点
                    let response = await fetch(`${this.apiBase}/health`);
                    
                    if (!response.ok) {
                        // 如果健康检查失败，尝试根路径
                        response = await fetch(`${this.apiBase}/`);
                    }

                    if (response.ok) {
                        this.apiStatus.className = 'status connected';
                        this.apiStatus.textContent = 'API服务正常';
                        this.log('API服务连通性检查通过');
                    } else {
                        throw new Error(`HTTP ${response.status}`);
                    }
                } catch (error) {
                    this.apiStatus.className = 'status disconnected';
                    this.apiStatus.textContent = 'API服务异常';
                    this.log(`API服务连通性检查失败: ${error.message}`);
                }
            }

            // 测试API连通性
            async testApiConnectivity() {
                this.testApiBtn.disabled = true;
                this.testApiBtn.textContent = '测试中...';
                
                try {
                    await this.checkApiStatus();
                    this.showSuccess('API连通性测试完成');
                } catch (error) {
                    this.showError(`API测试失败: ${error.message}`);
                } finally {
                    this.testApiBtn.disabled = false;
                    this.testApiBtn.textContent = '测试API连通性';
                }
            }

            // 获取用户信息
            async getUserInfo() {
                if (!this.authToken) {
                    this.showError('请先登录');
                    return;
                }

                this.getUserInfoBtn.disabled = true;
                this.getUserInfoBtn.textContent = '获取中...';

                try {
                    const response = await fetch(`${this.apiBase}/auth/me`, {
                        method: 'GET',
                        headers: {
                            'Authorization': `Bearer ${this.authToken}`,
                            'Content-Type': 'application/json'
                        }
                    });

                    const data = await response.json();
                    const result = this.handleApiResponse(data, response, '获取用户信息');

                    if (result.success && data.data) {
                        this.log(`用户信息: ${JSON.stringify(data.data.user, null, 2)}`);
                        if (data.data.sessions) {
                            this.log(`活跃会话数: ${data.data.sessions.length}`);
                        }
                    }
                } catch (error) {
                    this.showError(`获取用户信息失败: ${error.message}`);
                    this.log(`获取用户信息异常: ${error.message}`);
                } finally {
                    this.getUserInfoBtn.disabled = false;
                    this.getUserInfoBtn.textContent = '获取用户信息';
                }
            }

            // 获取AI模型列表
            async getModels() {
                this.getModelsBtn.disabled = true;
                this.getModelsBtn.textContent = '获取中...';

                try {
                    const response = await fetch(`${this.apiBase}/ai_chat/models`, {
                        method: 'GET',
                        headers: {
                            'Authorization': this.authToken ? `Bearer ${this.authToken}` : '',
                            'Content-Type': 'application/json'
                        }
                    });

                    const data = await response.json();
                    const result = this.handleApiResponse(data, response, '获取AI模型');

                    if (result.success && data.data) {
                        if (data.data.models) {
                            this.log(`可用AI模型: ${data.data.models.length}个`);
                            data.data.models.forEach(model => {
                                this.log(`- ${model.name} (${model.id}): ${model.description}`);
                            });
                        }
                    }
                } catch (error) {
                    this.showError(`获取AI模型列表失败: ${error.message}`);
                    this.log(`获取AI模型列表异常: ${error.message}`);
                } finally {
                    this.getModelsBtn.disabled = false;
                    this.getModelsBtn.textContent = '获取AI模型列表';
                }
            }

            async login() {
                const email = this.emailInput.value.trim();
                const password = this.passwordInput.value.trim();

                if (!email || !password) {
                    this.showError('请输入邮箱和密码');
                    return;
                }

                this.loginBtn.disabled = true;
                this.loginBtn.textContent = '登录中...';

                try {
                    const response = await fetch(`${this.apiBase}/auth/login`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            user: { email, password }
                        })
                    });

                    const data = await response.json();
                    const result = this.handleApiResponse(data, response, '登录');

                    if (result.success && data.data) {
                        this.authToken = data.data.token;
                        this.currentUser = data.data.user;
                        this.updateAuthStatus(true);
                        this.log(`登录成功: ${data.data.user.email}`);
                    } else {
                        this.log(`登录失败: ${result.error}`);
                    }
                } catch (error) {
                    this.showError(`登录请求失败: ${error.message}`);
                    this.log(`登录请求异常: ${error.message}`);
                } finally {
                    this.loginBtn.disabled = false;
                    this.loginBtn.textContent = '登录';
                }
            }

            async logout() {
                if (!this.authToken) return;

                try {
                    const response = await fetch(`${this.apiBase}/auth/logout`, {
                        method: 'DELETE',
                        headers: {
                            'Authorization': `Bearer ${this.authToken}`,
                            'Content-Type': 'application/json'
                        }
                    });

                    const data = await response.json();
                    this.handleApiResponse(data, response, '登出');
                } catch (error) {
                    this.log(`登出请求失败: ${error.message}`);
                }

                this.disconnectWebSocket();
                this.authToken = null;
                this.currentUser = null;
                this.currentRoom = null;
                this.updateAuthStatus(false);
                this.log('已登出');
                this.showSuccess('已登出');
            }

            async createRoom() {
                if (!this.authToken) {
                    this.showError('请先登录');
                    return;
                }

                const roomName = this.roomNameInput.value.trim();
                if (!roomName) {
                    this.showError('请输入聊天室名称');
                    return;
                }

                this.createRoomBtn.disabled = true;
                this.createRoomBtn.textContent = '创建中...';

                try {
                    const response = await fetch(`${this.apiBase}/ai_chat/rooms`, {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${this.authToken}`,
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            room: {
                                name: roomName,
                                description: '通过WebSocket测试页面创建的聊天室 (新版API)'
                            }
                        })
                    });

                    const data = await response.json();
                    const result = this.handleApiResponse(data, response, '创建聊天室');

                    if (result.success && data.data) {
                        this.currentRoom = data.data.room;
                        this.updateRoomInfo();
                        this.log(`聊天室创建成功: ${data.data.room.name} (ID: ${data.data.room.id})`);
                    } else {
                        this.log(`创建聊天室失败: ${result.error}`);
                    }
                } catch (error) {
                    this.showError(`创建聊天室请求失败: ${error.message}`);
                    this.log(`创建聊天室请求异常: ${error.message}`);
                } finally {
                    this.createRoomBtn.disabled = false;
                    this.createRoomBtn.textContent = '创建聊天室';
                }
            }

            connectWebSocket() {
                if (!this.authToken || !this.currentRoom) {
                    this.showError('请先登录并创建聊天室');
                    return;
                }

                if (this.cable) {
                    this.disconnectWebSocket();
                }

                this.connectBtn.disabled = true;
                this.connectBtn.textContent = '连接中...';
                this.updateWSStatus('connecting');

                try {
                    const cableUrl = `${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.host}/cable?token=${this.authToken}`;
                    this.log(`连接ActionCable: ${cableUrl}`);
                    
                    this.cable = ActionCable.createConsumer(cableUrl);

                    this.subscription = this.cable.subscriptions.create(
                        {
                            channel: 'AiChatChannel',
                            chat_room_id: this.currentRoom.id
                        },
                        {
                            connected: () => {
                                this.log('ActionCable连接已建立');
                                this.isConnected = true;
                                this.updateWSStatus('connected');
                                this.connectBtn.disabled = false;
                                this.connectBtn.textContent = '连接';
                                this.enableChat();
                                this.showSuccess('WebSocket频道订阅成功');
                            },

                            disconnected: () => {
                                this.log('ActionCable连接已断开');
                                this.isConnected = false;
                                this.updateWSStatus('disconnected');
                                this.connectBtn.disabled = false;
                                this.connectBtn.textContent = '连接';
                                this.disableChat();
                            },

                            received: (data) => {
                                this.handleWebSocketMessage(data);
                            }
                        }
                    );

                } catch (error) {
                    this.showError(`WebSocket连接失败: ${error.message}`);
                    this.log(`WebSocket连接异常: ${error.message}`);
                    this.connectBtn.disabled = false;
                    this.connectBtn.textContent = '连接';
                }
            }

            disconnectWebSocket() {
                if (this.subscription) {
                    this.subscription.unsubscribe();
                    this.subscription = null;
                }
                if (this.cable) {
                    this.cable.disconnect();
                    this.cable = null;
                }
                this.isConnected = false;
                this.updateWSStatus('disconnected');
                this.disableChat();
                this.log('WebSocket连接已断开');
            }

            handleWebSocketMessage(data) {
                this.log(`收到WebSocket消息: ${JSON.stringify(data).substring(0, 200)}...`);

                if (data.type === 'confirm_subscription') {
                    this.log('频道订阅确认');
                    return;
                }

                if (data.message || data.type) {
                    switch (data.type) {
                        case 'ai_streaming':
                            this.handleAIStreaming(data.message);
                            break;
                        case 'ai_response':
                            this.handleAIResponseComplete(data.message);
                            break;
                        case 'ai_error':
                            this.handleAIError(data.message, data.error);
                            break;
                        default:
                            if (data.message) {
                                const message = data.message;
                                switch (message.type) {
                                    case 'message':
                                        this.displayMessage(message.content, message.message_type, message.created_at);
                                        break;
                                    default:
                                        this.log(`未知消息类型: ${message.type || data.type}`);
                                }
                            } else {
                                this.log(`未知消息类型: ${data.type}`);
                            }
                    }
                }
            }

            handleAIStreaming(message) {
                const messageId = message.id;
                const content = message.content;
                
                let aiMessage = document.getElementById(`ai-message-${messageId}`);
                if (!aiMessage) {
                    aiMessage = this.createAIMessage(messageId);
                    this.messages.appendChild(aiMessage);
                }
                
                const contentDiv = aiMessage.querySelector('.content');
                contentDiv.textContent = content;
                
                this.scrollToBottom();
                this.log(`AI流式响应: 消息${messageId}, 内容长度: ${content.length}`);
            }

            handleAIResponseComplete(message) {
                const messageId = message.id;
                const finalContent = message.content;
                
                let aiMessage = document.getElementById(`ai-message-${messageId}`);
                if (!aiMessage) {
                    this.displayMessage(finalContent, 'ai', message.created_at);
                    return;
                }
                
                const contentDiv = aiMessage.querySelector('.content');
                contentDiv.textContent = finalContent;
                
                const metaDiv = aiMessage.querySelector('.meta');
                metaDiv.textContent = `AI响应 | Token: ${message.token_count || 'N/A'} | ${new Date(message.created_at || Date.now()).toLocaleTimeString()}`;
                
                aiMessage.classList.remove('typing');
                this.log(`AI响应完成 - 消息ID: ${messageId}, Token数: ${message.token_count || 'N/A'}`);
            }

            handleAIError(message, error) {
                const messageId = message.id;
                
                let aiMessage = document.getElementById(`ai-message-${messageId}`);
                if (!aiMessage) {
                    this.displayMessage(message.content, 'ai', message.created_at);
                    return;
                }
                
                const contentDiv = aiMessage.querySelector('.content');
                contentDiv.textContent = message.content;
                
                const metaDiv = aiMessage.querySelector('.meta');
                metaDiv.textContent = `AI错误 | ${new Date(message.created_at || Date.now()).toLocaleTimeString()}`;
                
                aiMessage.classList.remove('typing');
                aiMessage.style.borderLeft = '4px solid #dc3545';
                
                this.log(`AI响应错误 - 消息ID: ${messageId}, 错误: ${error.message}`);
                this.showError(`AI响应错误: ${error.message}`);
            }

            createAIMessage(messageId) {
                const messageDiv = document.createElement('div');
                messageDiv.className = 'message ai typing';
                messageDiv.id = `ai-message-${messageId}`;
                
                const contentDiv = document.createElement('div');
                contentDiv.className = 'content';
                
                const metaDiv = document.createElement('div');
                metaDiv.className = 'meta';
                metaDiv.textContent = 'AI正在回复...';
                
                messageDiv.appendChild(contentDiv);
                messageDiv.appendChild(metaDiv);
                
                return messageDiv;
            }

            async sendMessage() {
                const message = this.messageInput.value.trim();
                if (!message) return;

                if (!this.authToken || !this.currentRoom) {
                    this.showError('请先登录并创建聊天室');
                    return;
                }

                this.displayMessage(message, 'user');
                this.messageInput.value = '';

                try {
                    const response = await fetch(`${this.apiBase}/ai_chat/rooms/${this.currentRoom.id}/send_message`, {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${this.authToken}`,
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ message })
                    });

                    const data = await response.json();
                    const result = this.handleApiResponse(data, response, '发送消息');

                    if (result.success) {
                        this.log(`消息发送成功: ${data.message}`);
                    } else {
                        this.log(`发送消息失败: ${result.error}`);
                    }
                } catch (error) {
                    this.showError(`发送消息请求失败: ${error.message}`);
                    this.log(`发送消息请求异常: ${error.message}`);
                }
            }

            displayMessage(content, type, timestamp = null) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${type}`;

                const contentDiv = document.createElement('div');
                contentDiv.textContent = content;

                const metaDiv = document.createElement('div');
                metaDiv.className = 'meta';
                metaDiv.textContent = `${type === 'user' ? '用户' : 'AI'} | ${timestamp ? new Date(timestamp).toLocaleTimeString() : new Date().toLocaleTimeString()}`;

                messageDiv.appendChild(contentDiv);
                messageDiv.appendChild(metaDiv);

                this.messages.appendChild(messageDiv);
                this.scrollToBottom();
            }

            scrollToBottom() {
                this.messages.scrollTop = this.messages.scrollHeight;
            }

            updateAuthStatus(isLoggedIn) {
                if (isLoggedIn) {
                    this.authStatus.className = 'status connected';
                    this.authStatus.textContent = `已登录: ${this.currentUser.email}`;
                    this.loginBtn.disabled = true;
                    this.logoutBtn.disabled = false;
                    this.createRoomBtn.disabled = false;
                    this.getUserInfoBtn.disabled = false;
                } else {
                    this.authStatus.className = 'status disconnected';
                    this.authStatus.textContent = '未登录';
                    this.loginBtn.disabled = false;
                    this.logoutBtn.disabled = true;
                    this.createRoomBtn.disabled = true;
                    this.connectBtn.disabled = true;
                    this.getUserInfoBtn.disabled = true;
                    this.roomInfo.style.display = 'none';
                }
            }

            updateRoomInfo() {
                if (this.currentRoom) {
                    this.roomInfo.style.display = 'block';
                    this.roomInfo.innerHTML = `
                        <strong>聊天室:</strong> ${this.currentRoom.name}<br>
                        <strong>ID:</strong> ${this.currentRoom.id}<br>
                        <strong>状态:</strong> ${this.currentRoom.status}
                    `;
                    this.connectBtn.disabled = false;
                } else {
                    this.roomInfo.style.display = 'none';
                    this.connectBtn.disabled = true;
                }
            }

            updateWSStatus(status) {
                this.wsStatus.className = `status ${status}`;
                switch (status) {
                    case 'connected':
                        this.wsStatus.textContent = '已连接';
                        this.disconnectBtn.disabled = false;
                        break;
                    case 'connecting':
                        this.wsStatus.textContent = '连接中...';
                        this.disconnectBtn.disabled = true;
                        break;
                    case 'disconnected':
                        this.wsStatus.textContent = '未连接';
                        this.disconnectBtn.disabled = true;
                        break;
                }
            }

            enableChat() {
                this.messageInput.disabled = false;
                this.sendBtn.disabled = false;
            }

            disableChat() {
                this.messageInput.disabled = true;
                this.sendBtn.disabled = true;
            }

            log(message) {
                const timestamp = new Date().toLocaleTimeString();
                const logEntry = `[${timestamp}] ${message}\n`;
                this.logs.textContent += logEntry;
                this.logs.scrollTop = this.logs.scrollHeight;
            }

            clearLogs() {
                this.logs.textContent = '';
            }

            showError(message) {
                this.showNotification(message, 'error');
            }

            showSuccess(message) {
                this.showNotification(message, 'success');
            }

            showNotification(message, type) {
                const notification = document.createElement('div');
                notification.className = type;
                notification.textContent = message;
                notification.style.position = 'fixed';
                notification.style.top = '20px';
                notification.style.right = '20px';
                notification.style.zIndex = '1000';
                notification.style.maxWidth = '300px';
                notification.style.fontSize = '14px';
                
                document.body.appendChild(notification);
                
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 3000);
            }
        }

        // 初始化应用
        document.addEventListener('DOMContentLoaded', () => {
            new AIWebSocketTester();
        });
    </script>
</body>
</html>