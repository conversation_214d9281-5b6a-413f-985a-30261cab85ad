---
openapi: 3.0.1
info:
  title: Deep Sport API
  version: v1
  description: Deep Sport 运动应用后端API文档，包含用户管理、认证授权、AI聊天等功能
  contact:
    name: Deep Sport Team
    email: <EMAIL>
  license:
    name: MIT
paths:
  "/ai_chat/rooms":
    get:
      summary: 获取聊天室列表
      tags:
      - AI聊天
      description: 获取当前用户的AI聊天室列表
      responses:
        '200':
          description: 获取成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      rooms:
                        type: array
                        items:
                          type: object
                          properties:
                            id:
                              type: integer
                              description: 聊天室ID
                              example: 1
                            name:
                              type: string
                              description: 聊天室名称
                              example: 技术讨论
                            description:
                              type: string
                              description: 聊天室描述
                              example: 讨论技术相关话题
                            last_message:
                              type: object
                              properties:
                                id:
                                  type: integer
                                  description: 最后消息ID
                                content:
                                  type: string
                                  description: 最后消息内容
                                message_type:
                                  type: string
                                  enum:
                                  - user
                                  - assistant
                                  description: 消息类型
                                created_at:
                                  type: string
                                  format: date-time
                                  description: 创建时间
                            total_tokens:
                              type: integer
                              description: 总token使用量
                              example: 1500
                            participant_count:
                              type: integer
                              description: 参与者数量
                              example: 2
                            created_at:
                              type: string
                              format: date-time
                              description: 创建时间
                            updated_at:
                              type: string
                              format: date-time
                              description: 更新时间
        '401':
          description: 未授权
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
    post:
      summary: 创建聊天室
      tags:
      - AI聊天
      description: 创建新的AI聊天室
      parameters: []
      responses:
        '201':
          description: 创建成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: 聊天室创建成功
                  data:
                    type: object
                    properties:
                      room:
                        type: object
                        properties:
                          id:
                            type: integer
                            description: 聊天室ID
                          name:
                            type: string
                            description: 聊天室名称
                          description:
                            type: string
                            description: 聊天室描述
                          status:
                            type: string
                            description: 聊天室状态
                      session:
                        type: object
                        properties:
                          id:
                            type: integer
                            description: 会话ID
                          session_name:
                            type: string
                            description: 会话名称
                          model_config:
                            type: object
                            description: 模型配置
        '422':
          description: 创建失败
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '401':
          description: 未授权
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                room:
                  type: object
                  properties:
                    name:
                      type: string
                      description: 聊天室名称
                      example: 新的讨论
                    description:
                      type: string
                      description: 聊天室描述
                      example: 关于新技术的讨论
                  required:
                  - name
              required:
              - room
  "/ai_chat/rooms/{id}":
    get:
      summary: 获取聊天室详情
      tags:
      - AI聊天
      description: 获取指定聊天室的详情和消息历史
      parameters:
      - name: id
        in: path
        required: true
        description: 聊天室ID
        schema:
          type: integer
      responses:
        '200':
          description: 获取成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      room:
                        type: object
                        properties:
                          id:
                            type: integer
                            description: 聊天室ID
                          name:
                            type: string
                            description: 聊天室名称
                          description:
                            type: string
                            description: 聊天室描述
                          status:
                            type: string
                            description: 聊天室状态
                      messages:
                        type: array
                        items:
                          type: object
                          properties:
                            id:
                              type: integer
                              description: 消息ID
                            content:
                              type: string
                              description: 消息内容
                            message_type:
                              type: string
                              enum:
                              - user
                              - assistant
                              description: 消息类型
                            user:
                              type: object
                              properties:
                                id:
                                  type: integer
                                  description: 用户ID
                                name:
                                  type: string
                                  description: 用户名称
                                email:
                                  type: string
                                  description: 用户邮箱
                            token_count:
                              type: integer
                              description: Token数量
                            model_name:
                              type: string
                              description: AI模型名称
                            parent_message_id:
                              type: integer
                              description: 父消息ID
                            status:
                              type: string
                              description: 消息状态
                            created_at:
                              type: string
                              format: date-time
                              description: 创建时间
                      total_tokens:
                        type: integer
                        description: 总token使用量
        '404':
          description: 聊天室不存在
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '403':
          description: 权限不足
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '401':
          description: 未授权
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
  "/ai_chat/rooms/{id}/send_message":
    post:
      summary: 发送消息
      tags:
      - AI聊天
      description: 向AI聊天室发送消息
      parameters:
      - name: id
        in: path
        required: true
        description: 聊天室ID
        schema:
          type: integer
      - name: message
        in: query
        required: true
        description: 消息内容
        example: 你好，请介绍一下深度学习
        schema:
          type: string
      responses:
        '201':
          description: 消息发送成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: 消息发送成功，AI正在处理中
                  data:
                    type: object
                    properties:
                      user_message:
                        type: object
                        properties:
                          id:
                            type: integer
                            description: 消息ID
                          content:
                            type: string
                            description: 消息内容
                          message_type:
                            type: string
                            description: 消息类型
                          created_at:
                            type: string
                            format: date-time
                            description: 创建时间
        '400':
          description: 消息内容为空
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '404':
          description: 聊天室不存在
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '403':
          description: 权限不足
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '401':
          description: 未授权
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
  "/ai_chat/sessions":
    get:
      summary: 获取AI聊天会话列表
      tags:
      - AI聊天
      description: 获取当前用户的AI聊天会话列表
      responses:
        '200':
          description: 获取成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      sessions:
                        type: array
                        items:
                          type: object
                          properties:
                            id:
                              type: integer
                              description: 会话ID
                            session_name:
                              type: string
                              description: 会话名称
                            chat_room:
                              type: object
                              properties:
                                id:
                                  type: integer
                                  description: 聊天室ID
                                name:
                                  type: string
                                  description: 聊天室名称
                            total_tokens:
                              type: integer
                              description: 总token数
                            total_prompt_tokens:
                              type: integer
                              description: 提示token数
                            total_completion_tokens:
                              type: integer
                              description: 完成token数
                            last_message_at:
                              type: string
                              format: date-time
                              description: 最后消息时间
                            created_at:
                              type: string
                              format: date-time
                              description: 创建时间
                            cost_estimate:
                              type: number
                              format: float
                              description: 预估费用
        '401':
          description: 未授权
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
  "/ai_chat/statistics":
    get:
      summary: 获取AI聊天统计信息
      tags:
      - AI聊天
      description: 获取当前用户的AI聊天统计数据
      responses:
        '200':
          description: 获取成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      statistics:
                        type: object
                        properties:
                          total_rooms:
                            type: integer
                            description: 总聊天室数
                            example: 5
                          total_sessions:
                            type: integer
                            description: 总会话数
                            example: 8
                          total_messages:
                            type: integer
                            description: 总消息数
                            example: 150
                          total_tokens:
                            type: integer
                            description: 总token使用量
                            example: 15000
                          total_prompt_tokens:
                            type: integer
                            description: 总提示token数
                            example: 8000
                          total_completion_tokens:
                            type: integer
                            description: 总完成token数
                            example: 7000
                          last_chat_at:
                            type: string
                            format: date-time
                            description: 最后聊天时间
        '401':
          description: 未授权
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
  "/ai_chat/models":
    get:
      summary: 获取可用AI模型列表
      tags:
      - AI聊天
      description: 获取系统支持的AI模型列表
      responses:
        '200':
          description: 获取成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      models:
                        type: array
                        items:
                          type: object
                          properties:
                            id:
                              type: string
                              description: 模型ID
                              example: moonshot-v1-8k
                            name:
                              type: string
                              description: 模型名称
                              example: Moonshot v1 8K
                            description:
                              type: string
                              description: 模型描述
                            context_length:
                              type: integer
                              description: 上下文长度
                              example: 8192
                            input_price:
                              type: number
                              format: float
                              description: 输入价格/千token
                            output_price:
                              type: number
                              format: float
                              description: 输出价格/千token
        '401':
          description: 未授权
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
  "/auth/register":
    post:
      summary: 用户注册
      tags:
      - 认证管理
      description: 用户注册接口，支持邮箱注册，需要先发送邮箱验证码
      security: []
      parameters: []
      responses:
        '201':
          description: 注册成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: 注册成功，请查收邮箱验证邮件
                  data:
                    type: object
                    properties:
                      user:
                        "$ref": "#/components/schemas/User"
        '422':
          description: 注册失败
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                user:
                  type: object
                  properties:
                    email:
                      type: string
                      format: email
                      description: 用户邮箱
                      example: <EMAIL>
                    password:
                      type: string
                      minLength: 6
                      description: 密码（至少6位）
                      example: password123
                    name:
                      type: string
                      description: 用户姓名
                      example: 张三
                    weight:
                      type: number
                      format: float
                      description: 体重(kg)
                      example: 65.5
                      minimum: 1
                      maximum: 999
                    height:
                      type: number
                      format: float
                      description: 身高(cm)
                      example: 175.0
                      minimum: 1
                      maximum: 299
                    birthday:
                      type: string
                      format: date
                      description: 生日
                      example: '1990-01-01'
                    gender:
                      type: integer
                      enum:
                      - 0
                      - 1
                      - 2
                      description: 性别：0-未知，1-男，2-女
                      example: 1
                  required:
                  - email
                  - password
                  - name
                verification_code:
                  type: string
                  description: 邮箱验证码
                  example: '123456'
              required:
              - user
              - verification_code
  "/auth/verify_email":
    post:
      summary: 邮箱验证
      tags:
      - 认证管理
      description: 验证用户邮箱
      security: []
      parameters:
      - name: email
        in: query
        format: email
        required: true
        description: 用户邮箱
        schema:
          type: string
      - name: code
        in: query
        required: true
        description: 验证码
        schema:
          type: string
      responses:
        '200':
          description: 验证成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: 邮箱验证成功
        '400':
          description: 验证失败
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
  "/auth/login":
    post:
      summary: 用户登录或注册
      tags:
      - 认证管理
      description: 用户登录接口，支持密码登录和验证码登录。如果用户不存在且提供验证码，将自动注册用户。密码和验证码至少需要提供一个。
      security: []
      parameters: []
      responses:
        '200':
          description: 登录成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: 登录成功
                  data:
                    type: object
                    properties:
                      user:
                        "$ref": "#/components/schemas/User"
                      token:
                        type: string
                        description: JWT令牌
                        example: eyJhbGciOiJIUzI1NiJ9...
                      expires_at:
                        type: string
                        format: date-time
                        description: 令牌过期时间
        '401':
          description: 登录失败
          content:
            application/json:
              examples:
                example_0:
                  value:
                    password_auth_failed:
                      summary: 密码认证失败
                      value:
                        success: false
                        code: 401
                        message: 邮箱或密码错误
                        timestamp: '2023-12-01T10:00:00Z'
                    verification_code_failed:
                      summary: 验证码认证失败
                      value:
                        success: false
                        code: 401
                        message: 验证码无效或已过期
                        timestamp: '2023-12-01T10:00:00Z'
                    missing_credentials:
                      summary: 缺少认证信息
                      value:
                        success: false
                        code: 401
                        message: 密码和验证码至少需要提供一个
                        timestamp: '2023-12-01T10:00:00Z'
              schema:
                "$ref": "#/components/schemas/Error"
        '422':
          description: 参数验证失败（自动注册时）
          content:
            application/json:
              examples:
                example_0:
                  value:
                    validation_failed:
                      summary: 自动注册验证失败
                      value:
                        success: false
                        code: 422
                        message: 验证失败
                        details:
                          name:
                          - 姓名不能为空
                        timestamp: '2023-12-01T10:00:00Z'
              schema:
                "$ref": "#/components/schemas/Error"
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                user:
                  type: object
                  properties:
                    email:
                      type: string
                      format: email
                      description: 用户邮箱
                      example: <EMAIL>
                    password:
                      type: string
                      description: 密码（与验证码二选一）
                      example: password123
                    name:
                      type: string
                      description: 用户姓名（验证码登录时，用户不存在则自动注册需要）
                      example: 张三
                    weight:
                      type: number
                      format: float
                      description: 体重(kg)（可选）
                      example: 65.5
                      minimum: 1
                      maximum: 999
                    height:
                      type: number
                      format: float
                      description: 身高(cm)（可选）
                      example: 175.0
                      minimum: 1
                      maximum: 299
                    birthday:
                      type: string
                      format: date
                      description: 生日（可选）
                      example: '1990-01-01'
                    gender:
                      type: integer
                      enum:
                      - 0
                      - 1
                      - 2
                      description: 性别：0-未知，1-男，2-女（可选）
                      example: 1
                  required:
                  - email
                verification_code:
                  type: string
                  description: 邮箱验证码（与密码二选一）
                  example: '123456'
              required:
              - user
              oneOf:
              - properties:
                  user:
                    type: object
                    properties:
                      password:
                        type: string
                        minLength: 1
                    required:
                    - password
              - properties:
                  verification_code:
                    type: string
                    minLength: 1
                required:
                - verification_code
  "/auth/logout":
    delete:
      summary: 用户登出
      tags:
      - 认证管理
      description: 用户登出（当前设备）
      security:
      - bearerAuth: []
      responses:
        '200':
          description: 登出成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: 登出成功
        '401':
          description: 未授权
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
  "/auth/logout_all":
    delete:
      summary: 全设备登出
      tags:
      - 认证管理
      description: 用户在所有设备上登出
      security:
      - bearerAuth: []
      responses:
        '200':
          description: 全设备登出成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: 已从所有设备登出
        '401':
          description: 未授权
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
  "/auth/send_password_reset":
    post:
      summary: 发送重置密码邮件
      tags:
      - 认证管理
      description: 发送密码重置邮件
      security: []
      parameters:
      - name: email
        in: query
        format: email
        required: true
        description: 用户邮箱
        schema:
          type: string
      responses:
        '200':
          description: 邮件发送成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: 如果邮箱存在，密码重置链接已发送
  "/auth/reset_password":
    post:
      summary: 重置密码
      tags:
      - 认证管理
      description: 使用验证码重置密码
      security: []
      parameters: []
      responses:
        '200':
          description: 密码重置成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: 密码重置成功
        '422':
          description: 重置失败
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                  format: email
                  description: 用户邮箱
                  example: <EMAIL>
                code:
                  type: string
                  description: 验证码
                  example: '123456'
                new_password:
                  type: string
                  minLength: 6
                  description: 新密码
                  example: newpassword123
              required:
              - email
              - code
              - new_password
  "/auth/change_password":
    post:
      summary: 修改密码
      tags:
      - 认证管理
      description: 修改当前用户密码
      security:
      - bearerAuth: []
      parameters: []
      responses:
        '200':
          description: 密码修改成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: 密码修改成功
        '422':
          description: 修改失败
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '401':
          description: 未授权
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                current_password:
                  type: string
                  description: 当前密码
                  example: oldpassword123
                new_password:
                  type: string
                  minLength: 6
                  description: 新密码
                  example: newpassword123
              required:
              - current_password
              - new_password
  "/auth/me":
    get:
      summary: 获取当前用户信息
      tags:
      - 认证管理
      description: 获取当前登录用户的详细信息和会话列表
      security:
      - bearerAuth: []
      responses:
        '200':
          description: 获取成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      user:
                        "$ref": "#/components/schemas/User"
                      sessions:
                        type: array
                        items:
                          type: object
                          properties:
                            id:
                              type: integer
                              description: 会话ID
                            ip_address:
                              type: string
                              description: IP地址
                            user_agent:
                              type: string
                              description: 用户代理
                            created_at:
                              type: string
                              format: date-time
                              description: 创建时间
                            expires_at:
                              type: string
                              format: date-time
                              description: 过期时间
                            current:
                              type: boolean
                              description: 是否为当前会话
        '401':
          description: 未授权
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
  "/auth/refresh":
    get:
      summary: 刷新会话
      tags:
      - 认证管理
      description: 延长当前会话的有效期
      security:
      - bearerAuth: []
      responses:
        '200':
          description: 刷新成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: 会话已刷新
                  data:
                    type: object
                    properties:
                      expires_at:
                        type: string
                        format: date-time
                        description: 新的过期时间
        '401':
          description: 未授权
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
  "/auth/delete_account":
    delete:
      summary: 删除账户
      tags:
      - 认证管理
      description: 删除用户账户及所有相关数据，此操作不可恢复
      security:
      - Bearer: []
      responses:
        '200':
          description: 账户删除成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: 账户删除成功，所有相关数据已被永久删除
        '401':
          description: 未授权
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
  "/legal_documents/privacy_policy":
    get:
      summary: 获取隐私政策
      tags:
      - 法律文档
      description: 获取隐私政策内容，支持多语言
      parameters:
      - name: locale
        in: query
        required: false
        description: "语言设置，用于国际化支持：\n* `zh-CN` - 中文（简体）\n* `en` - English\n\n如果不指定，系统将按以下优先级自动选择：\n1.
          URL 参数 locale\n2. HTTP 头 Accept-Language\n3. 默认语言 zh-CN:\n * `zh-CN` \n
          * `en` \n "
        enum:
        - zh-CN
        - en
        example: zh-CN
        schema:
          type: string
      responses:
        '200':
          description: 获取成功（英文）
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  code:
                    type: integer
                    example: 200
                  message:
                    type: string
                    nullable: true
                    example:
                  data:
                    type: object
                    properties:
                      title:
                        type: string
                        description: 隐私政策标题
                        example: Privacy Policy
                      content:
                        type: string
                        description: 隐私政策内容（Markdown格式）
                        example: |-
                          # Privacy Policy

                          ## 1. Information Collection
                          We collect the following types of information...
                      last_updated:
                        type: string
                        description: 最后更新时间
                        example: August 19, 2025
                      version:
                        type: string
                        description: 版本号
                        example: '1.0'
                  timestamp:
                    type: string
                    format: date-time
                    description: 响应时间戳
  "/legal_documents/terms_of_service":
    get:
      summary: 获取服务条款
      tags:
      - 法律文档
      description: 获取服务条款内容，支持多语言
      parameters:
      - name: locale
        in: query
        required: false
        description: "语言设置，用于国际化支持：\n* `zh-CN` - 中文（简体）\n* `en` - English\n\n如果不指定，系统将按以下优先级自动选择：\n1.
          URL 参数 locale\n2. HTTP 头 Accept-Language\n3. 默认语言 zh-CN:\n * `zh-CN` \n
          * `en` \n "
        enum:
        - zh-CN
        - en
        example: zh-CN
        schema:
          type: string
      responses:
        '200':
          description: 获取成功（英文）
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  code:
                    type: integer
                    example: 200
                  message:
                    type: string
                    nullable: true
                    example:
                  data:
                    type: object
                    properties:
                      title:
                        type: string
                        description: 服务条款标题
                        example: Terms of Service
                      content:
                        type: string
                        description: 服务条款内容（Markdown格式）
                        example: |-
                          # Terms of Service

                          ## 1. Service Acceptance
                          By using our service, you agree to comply with these terms...
                      last_updated:
                        type: string
                        description: 最后更新时间
                        example: August 19, 2025
                      version:
                        type: string
                        description: 版本号
                        example: '1.0'
                  timestamp:
                    type: string
                    format: date-time
                    description: 响应时间戳
  "/questionnaire/options":
    get:
      summary: Get questionnaire options
      tags:
      - Questionnaire
      description: Get available options for questionnaire fields (exercise frequency,
        fitness goals, obstacles)
      responses:
        '200':
          description: Options retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  code:
                    type: integer
                    example: 200
                  message:
                    type: string
                    nullable: true
                  data:
                    type: object
                    properties:
                      exercise_frequencies:
                        type: array
                        items:
                          type: object
                          properties:
                            value:
                              type: string
                              example: occasionally
                            label:
                              type: string
                              example: 偶尔锻炼(0-2次/周)
                            description:
                              type: string
                              example: 0-2 times per week
                      fitness_goals:
                        type: array
                        items:
                          type: object
                          properties:
                            value:
                              type: string
                              example: weight_loss
                            label:
                              type: string
                              example: 减肥瘦身
                      obstacles:
                        type: array
                        items:
                          type: object
                          properties:
                            value:
                              type: string
                              example: lack_consistency
                            label:
                              type: string
                              example: 缺乏一致性
                  timestamp:
                    type: string
                    format: datetime
  "/questionnaire/submit":
    post:
      summary: Submit questionnaire for authenticated user
      tags:
      - Questionnaire
      description: Submit or update questionnaire data for the currently authenticated
        user
      security:
      - Bearer: []
      parameters: []
      responses:
        '200':
          description: Questionnaire updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  code:
                    type: integer
                    example: 200
                  message:
                    type: string
                    example: 问卷信息更新成功
                  data:
                    type: object
                    properties:
                      user:
                        "$ref": "#/components/schemas/User"
                  timestamp:
                    type: string
                    format: datetime
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '422':
          description: Validation failed
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ValidationError"
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                exercise_frequency:
                  type: string
                  enum:
                  - occasionally
                  - regular
                  - athlete
                  description: Exercise frequency per week
                fitness_goals:
                  type: array
                  items:
                    type: string
                    enum:
                    - weight_loss
                    - muscle_gain
                    - endurance
                    - strength
                    - flexibility
                    - health
                    - stress_relief
                    - competition
                  description: User fitness goals (can select multiple)
                obstacles:
                  type: array
                  items:
                    type: string
                    enum:
                    - lack_consistency
                    - unhealthy_work_habits
                    - lack_support
                    - busy_schedule
                    - lack_motivation
                  description: Obstacles preventing user from achieving goals (can
                    select multiple)
              required:
              - exercise_frequency
              - fitness_goals
              - obstacles
  "/questionnaire/submit_with_registration":
    post:
      summary: Submit questionnaire with user registration
      tags:
      - Questionnaire
      description: Register new user and submit questionnaire data in one step. Used
        for the questionnaire-first registration flow.
      parameters: []
      responses:
        '201':
          description: User registered and questionnaire submitted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  code:
                    type: integer
                    example: 201
                  message:
                    type: string
                    example: 注册成功，问卷信息已保存
                  data:
                    type: object
                    properties:
                      user:
                        "$ref": "#/components/schemas/User"
                      token:
                        type: string
                        description: Authentication token
                      expires_at:
                        type: string
                        format: datetime
                        description: Token expiration time
                  timestamp:
                    type: string
                    format: datetime
        '422':
          description: Validation failed
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ValidationError"
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                  format: email
                  description: User email address
                verification_code:
                  type: string
                  description: Email verification code
                name:
                  type: string
                  description: User full name
                gender:
                  type: string
                  enum:
                  - male
                  - female
                  - unknown
                  description: User gender
                height:
                  type: number
                  format: decimal
                  description: Height in cm
                weight:
                  type: number
                  format: decimal
                  description: Weight in kg
                birthday:
                  type: string
                  format: date
                  description: Date of birth
                exercise_frequency:
                  type: string
                  enum:
                  - occasionally
                  - regular
                  - athlete
                  description: Exercise frequency per week
                fitness_goals:
                  type: array
                  items:
                    type: string
                    enum:
                    - weight_loss
                    - muscle_gain
                    - endurance
                    - strength
                    - flexibility
                    - health
                    - stress_relief
                    - competition
                  description: User fitness goals (can select multiple)
                obstacles:
                  type: array
                  items:
                    type: string
                    enum:
                    - lack_consistency
                    - unhealthy_work_habits
                    - lack_support
                    - busy_schedule
                    - lack_motivation
                  description: Obstacles preventing user from achieving goals (can
                    select multiple)
              required:
              - email
              - verification_code
              - name
              - exercise_frequency
              - fitness_goals
              - obstacles
  "/users":
    get:
      summary: 获取用户列表
      tags:
      - 用户管理
      description: 获取系统中所有用户的列表（管理员权限）
      parameters:
      - name: search
        in: query
        required: false
        description: 搜索关键词（邮箱、姓名）
        schema:
          type: string
      - name: role
        in: query
        enum:
        - admin
        - user
        required: false
        description: "按角色筛选:\n * `admin` \n * `user` \n "
        schema:
          type: string
      - name: status
        in: query
        enum:
        - active
        - inactive
        - suspended
        required: false
        description: 按状态筛选
        schema:
          type: string
      - name: email_verified
        in: query
        required: false
        description: 按邮箱验证状态筛选
        schema:
          type: boolean
      - name: gender
        in: query
        enum:
        - 0
        - 1
        - 2
        required: false
        description: 按性别筛选：0-未知，1-男，2-女
        schema:
          type: integer
      - name: sort_by
        in: query
        required: false
        description: 排序字段
        schema:
          type: string
      - name: page
        in: query
        required: false
        description: 页码
        schema:
          type: integer
      - name: per_page
        in: query
        required: false
        description: 每页数量
        schema:
          type: integer
      responses:
        '200':
          description: 获取成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      users:
                        type: array
                        items:
                          "$ref": "#/components/schemas/User"
                  meta:
                    type: object
                    properties:
                      current_page:
                        type: integer
                        example: 1
                      total_pages:
                        type: integer
                        example: 10
                      total_count:
                        type: integer
                        example: 100
                      per_page:
                        type: integer
                        example: 10
        '401':
          description: 未授权
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '403':
          description: 权限不足
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
    post:
      summary: 创建用户
      tags:
      - 用户管理
      description: 创建新用户（管理员权限）
      parameters: []
      responses:
        '201':
          description: 创建成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: 用户创建成功
                  data:
                    type: object
                    properties:
                      user:
                        "$ref": "#/components/schemas/User"
        '422':
          description: 创建失败
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '401':
          description: 未授权
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '403':
          description: 权限不足
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                user:
                  type: object
                  properties:
                    email:
                      type: string
                      format: email
                      description: 用户邮箱
                      example: <EMAIL>
                    name:
                      type: string
                      description: 用户姓名
                      example: 新用户
                    password:
                      type: string
                      minLength: 6
                      description: 密码
                      example: password123
                    roles:
                      type: array
                      items:
                        type: string
                        enum:
                        - admin
                        - user
                      description: 用户角色数组
                      example:
                      - user
                    status:
                      type: string
                      enum:
                      - active
                      - inactive
                      description: 用户状态
                      example: active
                    skip_email_verification:
                      type: boolean
                      description: 跳过邮箱验证
                      example: false
                    weight:
                      type: number
                      format: float
                      description: 体重(kg)
                      example: 65.5
                    height:
                      type: number
                      format: float
                      description: 身高(cm)
                      example: 175.0
                    birthday:
                      type: string
                      format: date
                      description: 生日
                      example: '1990-01-01'
                    gender:
                      type: integer
                      enum:
                      - 0
                      - 1
                      - 2
                      description: 性别：0-未知，1-男，2-女
                      example: 1
                  required:
                  - email
                  - name
                  - password
              required:
              - user
  "/users/{id}":
    get:
      summary: 获取用户详情
      tags:
      - 用户管理
      description: 获取指定用户的详细信息
      parameters:
      - name: id
        in: path
        required: true
        description: 用户ID
        schema:
          type: integer
      responses:
        '200':
          description: 获取成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      user:
                        "$ref": "#/components/schemas/User"
                      sessions:
                        type: array
                        items:
                          type: object
                          properties:
                            id:
                              type: integer
                              description: 会话ID
                            ip_address:
                              type: string
                              description: IP地址
                            user_agent:
                              type: string
                              description: 用户代理
                            created_at:
                              type: string
                              format: date-time
                              description: 创建时间
                            expires_at:
                              type: string
                              format: date-time
                              description: 过期时间
                            active:
                              type: boolean
                              description: 是否活跃
        '404':
          description: 用户不存在
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '401':
          description: 未授权
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '403':
          description: 权限不足
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
    patch:
      summary: 更新用户信息
      tags:
      - 用户管理
      description: 更新用户信息
      parameters:
      - name: id
        in: path
        required: true
        description: 用户ID
        schema:
          type: integer
      responses:
        '200':
          description: 更新成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: 用户信息更新成功
                  data:
                    type: object
                    properties:
                      user:
                        "$ref": "#/components/schemas/User"
        '422':
          description: 更新失败
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '404':
          description: 用户不存在
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '401':
          description: 未授权
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '403':
          description: 权限不足
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                user:
                  type: object
                  properties:
                    email:
                      type: string
                      format: email
                      description: 用户邮箱
                    name:
                      type: string
                      description: 用户姓名
                    weight:
                      type: number
                      format: float
                      description: 体重(kg)
                    height:
                      type: number
                      format: float
                      description: 身高(cm)
                    birthday:
                      type: string
                      format: date
                      description: 生日
                    gender:
                      type: integer
                      enum:
                      - 0
                      - 1
                      - 2
                      description: 性别：0-未知，1-男，2-女
              required:
              - user
    delete:
      summary: 删除用户
      tags:
      - 用户管理
      description: 删除指定用户（管理员权限）
      parameters:
      - name: id
        in: path
        required: true
        description: 用户ID
        schema:
          type: integer
      responses:
        '200':
          description: 删除成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: 用户删除成功
        '404':
          description: 用户不存在
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '401':
          description: 未授权
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '403':
          description: 权限不足
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
  "/users/{id}/profile":
    patch:
      summary: 更新用户个人资料
      tags:
      - 用户管理
      description: 更新用户个人资料信息（用户可更新自己的资料）
      parameters:
      - name: id
        in: path
        required: true
        description: 用户ID
        schema:
          type: integer
      responses:
        '200':
          description: 更新成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: 个人资料更新成功
                  data:
                    type: object
                    properties:
                      user:
                        "$ref": "#/components/schemas/User"
        '422':
          description: 更新失败
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '404':
          description: 用户不存在
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '401':
          description: 未授权
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '403':
          description: 权限不足
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                user:
                  type: object
                  properties:
                    name:
                      type: string
                      description: 用户姓名
                      example: 张三
                    weight:
                      type: number
                      format: float
                      description: 体重(kg)
                      example: 65.5
                      minimum: 1
                      maximum: 999
                    height:
                      type: number
                      format: float
                      description: 身高(cm)
                      example: 175.0
                      minimum: 1
                      maximum: 299
                    birthday:
                      type: string
                      format: date
                      description: 生日
                      example: '1990-01-01'
                    gender:
                      type: integer
                      enum:
                      - 0
                      - 1
                      - 2
                      description: 性别：0-未知，1-男，2-女
                      example: 1
              required:
              - user
  "/users/{id}/role":
    patch:
      summary: 更新用户角色
      tags:
      - 用户管理
      description: 更新用户角色（管理员权限）
      parameters:
      - name: id
        in: path
        required: true
        description: 用户ID
        schema:
          type: integer
      responses:
        '200':
          description: 角色更新成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: 用户角色更新成功
                  data:
                    type: object
                    properties:
                      user:
                        "$ref": "#/components/schemas/User"
        '400':
          description: 角色无效
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '404':
          description: 用户不存在
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '401':
          description: 未授权
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '403':
          description: 权限不足
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                roles:
                  type: array
                  items:
                    type: string
                    enum:
                    - admin
                    - user
                  description: 新角色数组
                  example:
                  - user
                  - admin
              required:
              - roles
  "/users/{id}/roles/{role}":
    post:
      summary: 添加用户角色
      tags:
      - 用户管理
      description: 为用户添加新角色（管理员权限）
      parameters:
      - name: id
        in: path
        required: true
        description: 用户ID
        schema:
          type: integer
      - name: role
        in: path
        enum:
        - admin
        - user
        required: true
        description: "要添加的角色:\n * `admin` \n * `user` \n "
        schema:
          type: string
      responses:
        '200':
          description: 角色添加成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: 用户角色添加成功
                  data:
                    type: object
                    properties:
                      user:
                        "$ref": "#/components/schemas/User"
        '400':
          description: 角色无效或已存在
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '404':
          description: 用户不存在
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '401':
          description: 未授权
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '403':
          description: 权限不足
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
    delete:
      summary: 移除用户角色
      tags:
      - 用户管理
      description: 移除用户角色（管理员权限）
      parameters:
      - name: id
        in: path
        required: true
        description: 用户ID
        schema:
          type: integer
      - name: role
        in: path
        enum:
        - admin
        - user
        required: true
        description: "要移除的角色:\n * `admin` \n * `user` \n "
        schema:
          type: string
      responses:
        '200':
          description: 角色移除成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: 用户角色移除成功
                  data:
                    type: object
                    properties:
                      user:
                        "$ref": "#/components/schemas/User"
        '400':
          description: 角色不存在或为最后一个角色
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '404':
          description: 用户不存在
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '401':
          description: 未授权
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '403':
          description: 权限不足
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
  "/users/{id}/status":
    patch:
      summary: 更新用户状态
      tags:
      - 用户管理
      description: 更新用户状态（管理员权限）
      parameters:
      - name: id
        in: path
        required: true
        description: 用户ID
        schema:
          type: integer
      - name: status
        in: query
        enum:
        - active
        - inactive
        - suspended
        required: true
        description: "新状态:\n * `active` \n * `inactive` \n * `suspended` \n "
        schema:
          type: string
      responses:
        '200':
          description: 状态更新成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: 用户状态更新成功
                  data:
                    type: object
                    properties:
                      user:
                        "$ref": "#/components/schemas/User"
        '400':
          description: 状态无效
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '404':
          description: 用户不存在
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '401':
          description: 未授权
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '403':
          description: 权限不足
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
  "/users/{id}/reset_password":
    post:
      summary: 重置用户密码
      tags:
      - 用户管理
      description: 管理员重置用户密码
      parameters:
      - name: id
        in: path
        required: true
        description: 用户ID
        schema:
          type: integer
      - name: new_password
        in: query
        required: true
        description: 新密码
        schema:
          type: string
      responses:
        '200':
          description: 密码重置成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: 密码重置成功
        '422':
          description: 密码格式错误
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '404':
          description: 用户不存在
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '401':
          description: 未授权
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '403':
          description: 权限不足
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
  "/users/{id}/verify_email":
    post:
      summary: 验证用户邮箱
      tags:
      - 用户管理
      description: 管理员验证用户邮箱
      security:
      - bearerAuth: []
      parameters:
      - name: id
        in: path
        required: true
        description: 用户ID
        schema:
          type: integer
      responses:
        '200':
          description: 邮箱验证成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: 邮箱验证成功
        '404':
          description: 用户不存在
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '401':
          description: 未授权
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '403':
          description: 权限不足
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
  "/users/{id}/sessions":
    delete:
      summary: 撤销用户会话
      tags:
      - 用户管理
      description: 撤销用户的所有会话（管理员权限）
      security:
      - bearerAuth: []
      parameters:
      - name: id
        in: path
        required: true
        description: 用户ID
        schema:
          type: integer
      responses:
        '200':
          description: 会话撤销成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: 用户会话已撤销
        '404':
          description: 用户不存在
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '401':
          description: 未授权
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '403':
          description: 权限不足
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
  "/users/statistics":
    get:
      summary: 获取用户统计信息
      tags:
      - 用户管理
      description: 获取用户统计数据（管理员权限）
      security:
      - bearerAuth: []
      responses:
        '200':
          description: 获取成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      total_users:
                        type: integer
                        description: 总用户数
                        example: 1000
                      active_users:
                        type: integer
                        description: 活跃用户数
                        example: 800
                      verified_users:
                        type: integer
                        description: 已验证邮箱用户数
                        example: 900
                      admin_users:
                        type: integer
                        description: 管理员用户数
                        example: 5
                      recent_registrations:
                        type: integer
                        description: 最近7天注册用户数
                        example: 50
        '401':
          description: 未授权
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '403':
          description: 权限不足
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
  "/verification_codes/send":
    post:
      summary: 发送验证码
      tags:
      - 验证码管理
      description: 向指定邮箱发送验证码
      parameters:
      - name: email
        in: query
        format: email
        required: true
        description: 接收验证码的邮箱地址
        example: <EMAIL>
        schema:
          type: string
      - name: purpose
        in: query
        required: false
        description: "验证码用途:\n * `email_verification` \n * `password_reset` \n * `account_verification`
          \n "
        example: email_verification
        enum:
        - email_verification
        - password_reset
        - account_verification
        schema:
          type: string
      - name: locale
        in: query
        required: false
        description: 语言地区
        example: zh-CN
        enum:
        - zh-CN
        - en
        schema:
          type: string
      responses:
        '200':
          description: 验证码发送成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: 验证码已发送到您的邮箱，请查收
                  data:
                    type: object
                    properties:
                      email:
                        type: string
                        format: email
                        description: 邮箱地址
                      purpose:
                        type: string
                        description: 验证码用途
                      expires_at:
                        type: string
                        format: date-time
                        description: 过期时间
                      resend_after:
                        type: string
                        format: date-time
                        description: 可重新发送时间
        '400':
          description: 请求参数错误
          content:
            application/json:
              examples:
                example_0:
                  value:
                    invalid_email:
                      summary: 邮箱格式错误
                      value:
                        success: false
                        error: 邮箱地址格式不正确
                    missing_email:
                      summary: 邮箱地址必填
                      value:
                        success: false
                        error: 邮箱地址不能为空
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  error:
                    type: string
                    example: 邮箱地址格式不正确
        '429':
          description: 发送频率过高
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  error:
                    type: string
                    example: 验证码发送频率过高，请稍后再试
        '500':
          description: 发送失败
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  error:
                    type: string
                    example: 验证码发送失败，请稍后重试
  "/verification_codes/verify":
    post:
      summary: 验证验证码
      tags:
      - 验证码管理
      description: 验证邮箱验证码是否正确
      parameters:
      - name: email
        in: query
        format: email
        required: true
        description: 邮箱地址
        example: <EMAIL>
        schema:
          type: string
      - name: code
        in: query
        required: true
        description: 验证码
        example: '123456'
        schema:
          type: string
      - name: purpose
        in: query
        required: false
        description: "验证码用途:\n * `email_verification` \n * `password_reset` \n * `account_verification`
          \n "
        example: email_verification
        enum:
        - email_verification
        - password_reset
        - account_verification
        schema:
          type: string
      - name: locale
        in: query
        required: false
        description: 语言地区
        example: zh-CN
        enum:
        - zh-CN
        - en
        schema:
          type: string
      responses:
        '200':
          description: 验证成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: 验证码验证成功
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  error:
                    type: string
                    example: 邮箱地址和验证码不能为空
        '422':
          description: 验证失败
          content:
            application/json:
              examples:
                example_0:
                  value:
                    invalid_code:
                      summary: 验证码错误
                      value:
                        success: false
                        error: 验证码错误
                    expired_code:
                      summary: 验证码已过期
                      value:
                        success: false
                        error: 验证码已过期，请重新发送
                    code_not_found:
                      summary: 验证码不存在
                      value:
                        success: false
                        error: 验证码不存在或已使用
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  error:
                    type: string
                    example: 验证码错误或已过期
servers:
- url: http://localhost:3000
  description: 开发环境
- url: https://api.deepsport.com
  description: 生产环境
security:
- bearerAuth: []
  acceptLanguage: []
components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: token
      description: '会话令牌认证，格式: Bearer <token>'
    acceptLanguage:
      type: apiKey
      name: Accept-Language
      in: header
      description: 客户端语言偏好（如 zh-CN 或 en），将随所有请求发送
  parameters:
    AcceptLanguage:
      name: Accept-Language
      in: header
      description: '客户端偏好的语言，支持: zh-CN(简体中文), en(英文)'
      required: false
      schema:
        type: string
        enum:
        - zh-CN
        - en
        - zh-CN,zh;q=0.9,en;q=0.8
        - en,zh-CN;q=0.9
        default: zh-CN
      example: zh-CN
    ContentType:
      name: Content-Type
      in: header
      description: 请求内容类型
      required: false
      schema:
        type: string
        default: application/json
      example: application/json
  schemas:
    Error:
      type: object
      properties:
        error:
          type: string
          description: 错误信息
        code:
          type: integer
          description: 错误代码
      required:
      - error
    User:
      type: object
      properties:
        id:
          type: integer
          description: 用户ID
        email:
          type: string
          format: email
          description: 用户邮箱
        name:
          type: string
          description: 用户姓名
        display_name:
          type: string
          description: 显示名称
        roles:
          type: array
          items:
            type: string
            enum:
            - user
            - admin
          description: 用户角色数组
        primary_role:
          type: string
          enum:
          - user
          - admin
          description: 主要角色（最高权限角色）
        roles_text:
          type: string
          description: 角色显示文本
        status:
          type: string
          enum:
          - active
          - inactive
          - suspended
          description: 用户状态
        email_verified:
          type: boolean
          description: 邮箱是否已验证
        last_login_at:
          type: string
          format: date-time
          description: 最后登录时间
        weight:
          type: number
          format: float
          description: 体重(kg)
        height:
          type: number
          format: float
          description: 身高(cm)
        birthday:
          type: string
          format: date
          description: 生日
        gender:
          type: string
          enum:
          - unknown
          - male
          - female
          description: 性别
        gender_text:
          type: string
          description: 性别文本
        age:
          type: integer
          description: 年龄
        bmi:
          type: number
          format: float
          description: BMI指数
        bmi_status:
          type: string
          description: BMI状态
        exercise_frequency:
          type: string
          enum:
          - occasionally
          - regular
          - athlete
          description: 运动频率
        exercise_frequency_text:
          type: string
          description: 运动频率文本
        fitness_goals:
          type: array
          items:
            type: string
            enum:
            - weight_loss
            - muscle_gain
            - endurance
            - strength
            - flexibility
            - health
            - stress_relief
            - competition
          description: 健身目标数组
        obstacles:
          type: array
          items:
            type: string
            enum:
            - lack_consistency
            - unhealthy_work_habits
            - lack_support
            - busy_schedule
            - lack_motivation
          description: 阻碍因素数组
        questionnaire_completed:
          type: boolean
          description: 问卷是否已完成
        created_at:
          type: string
          format: date-time
          description: 创建时间
        updated_at:
          type: string
          format: date-time
          description: 更新时间
    ValidationError:
      type: object
      properties:
        success:
          type: boolean
          example: false
        code:
          type: integer
          example: 422
        message:
          type: string
          example: 数据验证失败
        details:
          type: object
          additionalProperties:
            type: array
            items:
              type: string
          description: 具体的验证错误信息
        timestamp:
          type: string
          format: date-time
