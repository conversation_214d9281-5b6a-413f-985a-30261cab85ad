# README

This README would normally document whatever steps are necessary to get the
application up and running.

Things you may want to cover:

* Ruby version

* System dependencies

* Configuration

* Database creation

* Database initialization

* How to run the test suite

* Services (job queues, cache servers, search engines, etc.)

* Deployment instructions

## Redis 配置

### 环境变量

本应用支持通过环境变量配置 Redis 连接：

- `REDIS_URL`: Sidekiq 使用的 Redis URL（默认: `redis://localhost:6379/0`）
- `REDIS_CACHE_URL`: Rails Cache 使用的 Redis URL（可选，如果不设置则使用默认缓存存储）
- `SIDEKIQ_USERNAME`: Sidekiq Web UI 用户名（生产环境，默认: `admin`）
- `SIDEKIQ_PASSWORD`: Sidekiq Web UI 密码（生产环境，默认: `changeme`）

### 示例配置

```bash
# 基本配置
export REDIS_URL=redis://localhost:6379/0
export REDIS_CACHE_URL=redis://localhost:6379/1

# 带认证的 Redis
export REDIS_URL=redis://username:password@hostname:port/database
export REDIS_CACHE_URL=redis://username:password@hostname:port/database

# 生产环境 Sidekiq Web UI
export SIDEKIQ_USERNAME=your_username
export SIDEKIQ_PASSWORD=your_secure_password
```

### 缓存行为

- **Development**: 如果设置了 `REDIS_CACHE_URL`，使用 Redis 缓存；否则使用内存缓存
- **Production**: 如果设置了 `REDIS_CACHE_URL`，使用 Redis 缓存；否则使用 Solid Cache
- **Test**: 使用 null store（无缓存）

* ...
```markdown
这是一个 rails 项目，接口文档使用的是 rswag ,swaager.yaml 是生成的不要修改，
swagger 相关的生成在 spec 文件夹下，添加功能需要补充接口文档的生成。
接口和service都有统一的返回约定的，app/controllers/concerns/controller_response.rb，app/services/concerns/api_response.rb。
现在需要添加一个注销的功能，用户注销之后，所有相关的数据都需要删除，
uni-starter 文件夹是一个 uniapp 项目，需要在 ucenter 增加一个 Personal details 功能, 字段可以参考用户模型


这是一个 rails 项目，接口文档使用的是 rswag ,swaager.yaml 是生成的不要修改，
swagger 相关的生成在 spec 文件夹下，添加功能需要补充接口文档的生成。
接口和service都有统一的返回约定的，app/controllers/concerns/controller_response.rb，app/services/concerns/api_response.rb。
uni-starter 文件夹是一个 uniapp 项目，现在需要调整登录模型，如果没有登录，首先应该是出现的一个登录和问卷填写选择界面，
填写问卷的案例在屏幕中间偏下，上面是app 介绍的图和文字，最下面有小字可以进行登录，
选择登录的流程就是输入邮箱，下一个页面输入验证码然后登录成功。
填问卷的按钮是Get Started,点击进入下一个页面，选择性别，
再下一个页面选择身高和体重，身高在左边滑动选择，体重在右边滑动选择，支持切换英制单位和公制。
再下一个页面选择生日，再下一个选择每周锻炼几次，三个选项，0-2(偶尔锻炼)，3-5(每周几次锻炼)，6+(专注的运动员)
再下一个页面，选择是什么阻止你实现目标，5个选项，缺乏一致性，不健康的工作习惯，缺乏支持，日程繁忙，缺乏运动灵感，
再下一个页面，感谢你的信任，现在让我们为你个性化 Motio,下面提醒 你的饮食和安全对我们很重要。我们承诺失踪保护你的个人信息安全与隐私。
再下一个页面，和登录界面一样，输入邮箱，下一个页面输入验证码然后登录成功，填写问卷的结果需要和通过登录接口传入。
这个app是全英文的，这些功能全部用英文实现

uni-starter 文件夹是一个 uniapp 项目，现在需要优化首页，首页的中间应该是 这个 app 的介绍，并且介绍拍照或者上传图片进行分析运动，
下面有两个 icon 按钮，一个是拍照，一个是上传图片，
拍照或上传最多9张图片，拍照上传后在首页中显示选中或者拍照的图片，点击图片可以查看图片，还可以删除图片，
最下面有一个分析按钮，还有一个清空按钮，点击分析按钮，把图片上传到服务器，然后调用服务器接口，提示分析中，然后跳转到分析页面，等待分析结果
这个app是全英文的，这些功能全部用英文实现

完善分析页面，分析页面上面可以快速切换日期，
可以快速一周的切换，提供一个日历选择，选好日期后，日期是这一行的中间一个
下面可以显示多条分析记录，
分析记录点击展开，展开后可以继续提问，
分析结果包含，
整体表现，
配速与心率分析(每公里)，
心率区间分布(跑步时心率区间，放松整理阶段)，
热量消耗与能量代谢分析，
步频与跑步效率，环境系数影响，
运动效果与身体机能提升，
训练质量评价与建议，
周计划安排，
营养建议(一周)，
原计划VS实际执行，
运动表现分析。
这些展示类似一个报告，在报告上有这次分析的图片，点击图片可以查看图片，
在报告下面有输入框和发送按钮，输入框可以输入问题，发送按钮可以发送出去，让ai 继续分析，ai 再次回复就类似聊天对话效果


这是一个 rails 项目，接口文档使用的是 rswag ,swaager.yaml 是生成的不要修改，
swagger 相关的生成在 spec 文件夹下，添加功能需要补充接口文档的生成。
接口和service都有统一的返回约定的，app/controllers/concerns/controller_response.rb，app/services/concerns/api_response.rb。
uni-starter 文件夹是一个 uniapp 项目，uniapp API 调用在 uni-starter/common/api.js 文件中，
现在需要完成首页提交图片进行分析的前后端功能，需要完成提交后进入到分析页的前后端功能，
提交的图片需要上传到 aws s3 存储，uniapp 可以使用 uni.uploadFile 上传图片，支持多张图片上传。
实现的是需要一次上传多张图片，然后调用 ai 模型进行图片分析，然后返回分析结果，
分析结果包含，
整体表现，
配速与心率分析(每公里)，
心率区间分布(跑步时心率区间，放松整理阶段)，
热量消耗与能量代谢分析，
步频与跑步效率，环境系数影响，
运动效果与身体机能提升，
训练质量评价与建议，
周计划安排，
营养建议(一周)，
原计划VS实际执行，
运动表现分析。
ai 咨询的提示词在 uni-starter/prompts/analysis_report_prompt.md 文件，
ai 模型调用参考 app/services/ai_chat_service.rb

这是一个 rails 项目，现在还是测试阶段，db 可以删除清空，合并 db migrate 文件夹

./bin/rails db:drop && ./bin/rails db:create && ./bin/rails db:migrate && ./bin/rails db:seed
bundle exec rake rswag:specs:swaggerize
 ```