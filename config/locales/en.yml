en:
  hello: "Hello world"
  
  auth:
    login_required: "Please log in to continue"
    invalid_credentials: "Invalid email or password"
    registration_disabled: "Registration is disabled"
    email_already_taken: "Email already taken"
    password_mismatch: "Password confirmation doesn't match password"
    verification_code_sent: "Verification code sent successfully"
    verification_code_invalid: "Invalid or expired verification code"
    password_reset_sent: "Password reset instructions sent to your email"
    password_changed: "Password changed successfully"
    login_successful: "Logged in successfully"
    logout_successful: "Logged out successfully"
    registration_successful: "Registration successful"
    logout_all_successful: "Logged out from all devices successfully"
    account_status_inactive: "Your account is inactive"
    account_status_banned: "Your account has been banned"
    token_expired: "Token has expired"
    token_invalid: "Invalid token"
    email_verified: "Email verified successfully"
    email_already_verified: "Email is already verified"
    
    success:
      register: "Registration successful, please check your email and verify"
      login: "Login successful"
      logout: "Logout successful"
      logout_all: "Logged out from all devices"
      email_verified: "Email verification successful"
      password_reset_sent: "Password reset email has been sent"
      password_reset: "Password reset successful"
      password_changed: "Password changed successful"
      session_refreshed: "Session refreshed"
      account_deleted: "Account deleted successfully, all related data has been permanently removed"
      
    errors:
      register_failed: "Registration failed"
      email_empty: "Email cannot be empty"
      password_empty: "Password cannot be empty"
      password_or_code_required: "Either password or verification code is required"
      email_not_verified: "Please verify your email first"
      account_disabled: "Account has been disabled"
      invalid_credentials: "Invalid email or password"
      session_not_found: "Session not found"
      user_not_found: "User not found"
      invalid_token: "Reset token is invalid or expired"
      invalid_password: "Current password is incorrect"
      password_invalid: "Password is incorrect"
      session_invalid: "Session is invalid"
      password_reset_message: "If the email exists, a password reset email has been sent"
      account_deletion_failed: "Account deletion failed, please try again later"

  verification_code:
    invalid_type: "Invalid verification code type"
    invalid_format: "Invalid verification code format"
    expired: "Verification code has expired"
    not_found: "Verification code not found"
    already_used: "Verification code has already been used"
    send_failed: "Failed to send verification code"
    sent_successfully: "Verification code sent successfully"
    verified_successfully: "Verification code verified successfully"
    
    purpose:
      email_verification: "Email Verification"
      password_reset: "Password Reset"
      login: "Login Verification"
      default: "Identity Verification"
    
    mailer:
      subject: "%{purpose} Code - %{code}"
      greeting: "Hello!"
      instruction: "You are performing %{purpose} operation, please use the following verification code:"
      code_label: "Verification Code:"
      important_notice: "Important Notice:"
      notice_items:
        - "Verification code is valid for 10 minutes"
        - "Verification code can only be used once"
        - "Please do not share the verification code with others"
        - "If this is not your operation, please ignore this email"
      support: "If you have any questions, please contact our customer service team."
      footer_notice: "This email is sent automatically by the system, please do not reply directly."
      copyright: "© %{year} %{company} All rights reserved"
    
    success:
      sent: "Verification code has been sent, please check your email"
      verified: "Verification successful"
    
    errors:
      too_frequent: "Sending too frequently, please try again later"
      send_failed: "Sending failed, please try again later"
      invalid: "Verification code is invalid or expired"
      email_required: "Email address cannot be empty"
      email_format: "Email address format is incorrect"
      params_required: "Email and verification code cannot be empty"

  user:
    updated: "User information updated successfully"
    role_updated: "User role updated successfully"
    status_updated: "User status updated successfully"
    profile_updated: "Profile updated successfully"
    not_found: "User not found"
    invalid_role: "Invalid role"
    invalid_status: "Invalid status"
    cannot_modify_self: "You cannot modify your own account"
    password_reset: "Password reset successfully"
    sessions_revoked: "All sessions revoked successfully"
    role_added: "Role added successfully"
    role_removed: "Role removed successfully"
    role_already_exists: "User already has this role"
    role_not_found: "User does not have this role"
    
    roles:
      user: "User"
      admin: "Administrator"
    
    gender:
      unknown: "Unknown"
      male: "Male"
      female: "Female"
      other: "Other"
      prefer_not_to_say: "Prefer not to say"
    
    exercise_frequency:
      occasionally: "Occasionally"
      regular: "Regular"
      athlete: "Athlete"
    
    fitness_goals:
      weight_loss: "Weight Loss"
      muscle_gain: "Muscle Gain"
      endurance: "Endurance"
      strength: "Strength"
      flexibility: "Flexibility"
      health: "General Health"
      stress_relief: "Stress Relief"
      competition: "Competition"
    
    obstacles:
      lack_consistency: "Lack of Consistency"
      unhealthy_work_habits: "Unhealthy Work Habits"
      lack_support: "Lack of Support"
      busy_schedule: "Busy Schedule"
      lack_motivation: "Lack of Motivation"
    
    bmi_status:
      unknown: "Unknown"
      underweight: "Underweight"
      normal: "Normal"
      overweight: "Overweight"
      obese: "Obese"
    
    validations:
      birthday_future: "Birthday cannot be in the future"
      invalid_roles: "Invalid roles: %{roles}"

  users:
    success:
      created: "User created successfully"
      updated: "User information updated successfully"
      roles_updated: "User roles updated successfully"
      role_added: "Role %{role} added successfully"
      role_removed: "Role %{role} removed successfully"
      role_updated: "User role updated successfully"
      status_updated: "User status updated successfully"
      deleted: "User deleted successfully"
      password_reset: "User password reset successfully"
      email_verified: "User email verified successfully"
      sessions_revoked: "Revoked %{count} sessions"
      
    errors:
      create_failed: "User creation failed"
      update_failed: "User information update failed"
      not_found: "User not found"
      invalid_role: "Invalid user role"
      invalid_roles: "Invalid roles: %{roles}"
      invalid_role_or_already_exists: "Role %{role} is invalid or already exists"
      role_not_found_or_last_role: "Role %{role} not found or is the last role"
      invalid_status: "Invalid user status"
      cannot_delete_last_admin: "Cannot delete the last administrator"

  errors:
    messages:
      record_invalid: "Validation failed: %{errors}"
      record_not_found: "Record not found"
      access_denied: "Access denied"
      parameter_missing: "Parameter missing: %{param}"
      invalid_request: "Invalid request"
      internal_server_error: "Internal server error"
    validation_failed: "Data validation failed"
    resource_not_found: "The specified %{resource} was not found"
    not_found: "Resource not found"
    unauthorized: "Unauthorized access"
    forbidden: "Insufficient permissions"
    parameter_missing: "Required parameter missing: %{param}"
    invalid_argument: "Invalid parameter"

  company:
    name: "Motion"
    name_cn: "Motion"

  authentication:
    errors:
      login_required: "Login required"
      admin_required: "Admin privileges required"
      role_required: "Role %{role} required"
      any_role_required: "Any of the following roles required: %{roles}"
      permission_denied: "Permission denied"

  ai_chat:
    success:
      session_created: "Chat session created successfully"
    errors:
      session_creation_failed: "Chat session creation failed"
      message_empty: "Message content cannot be empty"
      session_not_found: "Chat session not found"
      access_denied: "You don't have permission to access this chat room"
  
  exercise_analyses:
    success:
      created: "Exercise analysis request created successfully"
      deleted: "Exercise analysis record deleted successfully"
      completed: "Exercise analysis completed successfully"
    
    errors:
      not_found: "Exercise analysis not found"
      create_failed: "Failed to create exercise analysis request"
      no_images_provided: "No images provided for analysis"
      image_upload_failed: "Failed to upload images"
      analysis_failed: "Exercise analysis failed"
    
    status:
      pending: "Pending Analysis"
      analyzing: "Analyzing"
      completed: "Completed"
      failed: "Failed"

  profile:
    success:
      updated: "Profile updated successfully"
    errors:
      update_failed: "Profile update failed"

  services:
    errors:
      user_not_found: "User not found"

  legal_documents:
    privacy_policy:
      title: "Privacy Policy"
      version: "1.0"
      last_updated: "August 19, 2025"
      content: |
        # Privacy Policy

        ## 1. Information Collection
        We collect the following types of information:
        - Personal information you voluntarily provide (registration info, contact details)
        - Automatically collected usage information (device info, usage patterns)
        - Information from third-party services

        ## 2. Information Usage
        We use the collected information to:
        - Provide and improve our services
        - Communicate with you
        - Protect user safety
        - Comply with legal requirements

        ## 3. Information Sharing
        We do not sell your personal information. We only share information in the following cases:
        - With your explicit consent
        - Legal requirements
        - To protect our rights and safety

        ## 4. Data Security
        We employ industry-standard security measures to protect your information, including:
        - Data encryption
        - Access control
        - Regular security audits

        ## 5. Your Rights
        You have the right to:
        - Access your personal information
        - Correct inaccurate information
        - Delete your personal information
        - Restrict information processing

        ## 6. Contact Us
        For any privacy-related questions, please contact us:
        Email: <EMAIL>

    terms_of_service:
      title: "Terms of Service"
      version: "1.0"
      last_updated: "August 19, 2025"
      content: |
        # Terms of Service

        ## 1. Service Acceptance
        By using our service, you agree to comply with these terms. If you do not agree to these terms, please do not use our service.

        ## 2. Service Description
        DeepSport is a sports data analysis and management platform that provides:
        - Sports data recording and analysis
        - AI intelligent chat services
        - Personal health management

        ## 3. User Responsibilities
        You agree to:
        - Provide accurate and truthful information
        - Not abuse our services
        - Comply with applicable laws and regulations
        - Protect your account security

        ## 4. Prohibited Activities
        You may not:
        - Upload malicious content
        - Infringe on others' rights
        - Interfere with normal service operation
        - Conduct unauthorized access

        ## 5. Intellectual Property
        Our services contain content protected by intellectual property laws. You may not:
        - Copy, modify, or distribute our content
        - Reverse engineer our software
        - Remove copyright notices

        ## 6. Disclaimers
        Our services are provided "as is" without any express or implied warranties.

        ## 7. Limitation of Liability
        To the maximum extent permitted by law, we shall not be liable for any indirect, incidental, or punitive damages.

        ## 8. Terms Changes
        We reserve the right to modify these terms at any time. Major changes will be communicated via email or in-app notifications.

        ## 9. Contact Us
        For any terms of service related questions, please contact us:
        Email: <EMAIL>