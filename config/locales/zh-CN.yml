zh-CN:
  hello: "你好，世界"
  
  errors:
    validation_failed: "数据验证失败"
    resource_not_found: "找不到指定的%{resource}"
    not_found: "资源不存在"
    unauthorized: "未授权访问"
    forbidden: "权限不足"
    internal_server_error: "服务器内部错误"
    parameter_missing: "缺少必需参数：%{param}"
    invalid_argument: "参数无效"
  
  company:
    name: "Motion"
    name_en: "Motion"
    
  verification_code:
    purpose:
      email_verification: "邮箱验证"
      password_reset: "密码重置" 
      login: "登录验证"
      default: "身份验证"
    
    mailer:
      subject: "%{purpose}验证码 - %{code}"
      greeting: "您好！"
      instruction: "您正在进行%{purpose}操作，请使用以下验证码："
      code_label: "验证码："
      important_notice: "重要提示："
      notice_items:
        - "验证码有效期为 10分钟"
        - "验证码仅可使用一次"
        - "请勿将验证码泄露给他人"
        - "如非本人操作，请忽略此邮件"
      support: "如果您有任何疑问，请联系我们的客服团队。"
      footer_notice: "此邮件由系统自动发送，请勿直接回复。"
      copyright: "© %{year} %{company} 版权所有"
    
    success:
      sent: "验证码已发送，请查收邮件"
      verified: "验证成功"
    
    errors:
      too_frequent: "发送过于频繁，请稍后再试"
      send_failed: "发送失败，请稍后重试"
      invalid: "验证码无效或已过期"
      email_required: "邮箱地址不能为空"
      email_format: "邮箱地址格式不正确"
      params_required: "邮箱和验证码不能为空"

  auth:
    success:
      register: "注册成功"
      login: "登录成功"
      logout: "登出成功"
      logout_all: "已登出所有设备"
      email_verified: "邮箱验证成功"
      password_reset_sent: "密码重置邮件已发送"
      password_reset: "密码重置成功"
      password_changed: "密码修改成功"
      session_refreshed: "会话已刷新"
      account_deleted: "账户删除成功，所有相关数据已被永久删除"
      
    errors:
      register_failed: "注册失败"
      email_empty: "邮箱不能为空"
      password_empty: "密码不能为空"
      password_or_code_required: "密码和验证码至少需要提供一个"
      email_not_verified: "请先验证邮箱"
      account_disabled: "账户已被禁用"
      invalid_credentials: "邮箱或密码错误"
      session_not_found: "会话不存在"
      user_not_found: "用户不存在"
      invalid_token: "重置令牌无效或已过期"
      invalid_password: "当前密码错误"
      password_invalid: "密码错误"
      session_invalid: "会话无效"
      password_reset_message: "如果邮箱存在，密码重置邮件已发送"
      account_deletion_failed: "账户删除失败，请稍后重试"

  users:
    success:
      created: "用户创建成功"
      updated: "用户信息更新成功"
      roles_updated: "用户角色更新成功"
      role_added: "角色 %{role} 添加成功"
      role_removed: "角色 %{role} 移除成功"
      role_updated: "用户角色更新成功"
      status_updated: "用户状态更新成功"
      deleted: "用户删除成功"
      password_reset: "用户密码重置成功"
      email_verified: "用户邮箱验证成功"
      sessions_revoked: "已撤销 %{count} 个会话"
      
    errors:
      create_failed: "用户创建失败"
      update_failed: "用户信息更新失败"
      not_found: "用户不存在"
      invalid_role: "无效的用户角色"
      invalid_roles: "无效的角色：%{roles}"
      invalid_role_or_already_exists: "角色 %{role} 无效或已存在"
      role_not_found_or_last_role: "角色 %{role} 不存在或为最后一个角色"
      invalid_status: "无效的用户状态"
      cannot_delete_last_admin: "不能删除最后一个管理员"

  user:
    roles:
      user: "普通用户"
      admin: "管理员"
    
    gender:
      unknown: "未知"
      male: "男"
      female: "女"
    
    exercise_frequency:
      unknown: "未知"
      occasionally: "偶尔锻炼(0-2次/周)"
      regular: "定期锻炼(3-5次/周)"
      athlete: "专业运动员(6+次/周)"
    
    fitness_goals:
      weight_loss: "减肥瘦身"
      muscle_gain: "增肌塑形"
      endurance: "提高耐力"
      strength: "增强力量"
      flexibility: "提高柔韧性"
      health: "保持健康"
      stress_relief: "缓解压力"
      competition: "比赛准备"
    
    obstacles:
      lack_consistency: "缺乏一致性"
      unhealthy_work_habits: "不健康的工作习惯"
      lack_support: "缺乏支持"
      busy_schedule: "日程繁忙"
      lack_motivation: "缺乏运动灵感"
      financial_constraints: "经济限制"
      physical_limitations: "身体限制"
      lack_knowledge: "缺乏专业知识"
    
    bmi_status:
      unknown: "未知"
      underweight: "偏瘦"
      normal: "正常"
      overweight: "偏胖"
      obese: "肥胖"
    
    validations:
      birthday_future: "生日不能是未来日期"
      invalid_roles: "无效的角色：%{roles}"

  authentication:
    errors:
      login_required: "需要登录"
      admin_required: "需要管理员权限"
      role_required: "需要 %{role} 角色权限"
      any_role_required: "需要以下任一角色权限：%{roles}"
      permission_denied: "权限不足"

  ai_chat:
    success:
      session_created: "聊天室创建成功"
    errors:
      session_creation_failed: "聊天室创建失败"
      message_empty: "消息内容不能为空"
      session_not_found: "聊天室不存在"
      access_denied: "您没有访问此聊天室的权限"

  profile:
    success:
      updated: "个人资料更新成功"
    errors:
      update_failed: "个人资料更新失败"

  questionnaire:
    success:
      updated: "问卷信息更新成功"
      registered_with_questionnaire: "注册成功，问卷信息已保存"
    errors:
      update_failed: "问卷信息更新失败"
      registration_failed: "注册失败"
    validations:
      invalid_data: "问卷数据验证失败"
      invalid_exercise_frequency: "运动频率选择无效"
      fitness_goals_required: "请至少选择一个健身目标"
      invalid_fitness_goals: "无效的健身目标：%{goals}"
      obstacles_required: "请至少选择一个阻碍因素"
      invalid_obstacles: "无效的阻碍因素：%{obstacles}"

  services:
    errors:
      user_not_found: "用户不存在"

  legal_documents:
    privacy_policy:
      title: "隐私政策"
      version: "1.0"
      last_updated: "2025年8月19日"
      content: |
        # 隐私政策

        ## 1. 信息收集
        我们收集以下类型的信息：
        - 您主动提供的个人信息（如注册信息、联系方式）
        - 自动收集的使用信息（如设备信息、使用习惯）
        - 第三方服务提供的信息

        ## 2. 信息使用
        我们使用收集的信息用于：
        - 提供和改善我们的服务
        - 与您沟通
        - 保护用户安全
        - 遵守法律要求

        ## 3. 信息分享
        我们不会出售您的个人信息。我们仅在以下情况下分享信息：
        - 获得您的明确同意
        - 法律要求
        - 保护我们的权利和安全

        ## 4. 数据安全
        我们采用行业标准的安全措施保护您的信息，包括：
        - 数据加密
        - 访问控制
        - 定期安全审计

        ## 5. 您的权利
        您有权：
        - 访问您的个人信息
        - 更正不准确的信息
        - 删除您的个人信息
        - 限制信息处理

        ## 6. 联系我们
        如有任何隐私相关问题，请联系我们：
        邮箱：<EMAIL>

    terms_of_service:
      title: "服务条款"
      version: "1.0"
      last_updated: "2025年8月19日"
      content: |
        # 服务条款

        ## 1. 服务接受
        通过使用我们的服务，您同意遵守这些条款。如果您不同意这些条款，请不要使用我们的服务。

        ## 2. 服务描述
        动数潜（DeepSport）是一个运动数据分析和管理平台，提供：
        - 运动数据记录和分析
        - AI智能聊天服务
        - 个人健康管理

        ## 3. 用户责任
        您同意：
        - 提供准确、真实的信息
        - 不滥用我们的服务
        - 遵守适用的法律法规
        - 保护您的账户安全

        ## 4. 禁止行为
        您不得：
        - 上传恶意内容
        - 侵犯他人权利
        - 干扰服务正常运行
        - 进行未授权的访问

        ## 5. 知识产权
        我们的服务包含受知识产权法保护的内容。您不得：
        - 复制、修改或分发我们的内容
        - 反向工程我们的软件
        - 移除版权声明

        ## 6. 免责声明
        我们的服务按"现状"提供，不提供任何明示或暗示的担保。

        ## 7. 责任限制
        在法律允许的最大范围内，我们不承担任何间接、附带或惩罚性损害的责任。

        ## 8. 条款变更
        我们保留随时修改这些条款的权利。重大变更将通过邮件或应用内通知告知您。

        ## 9. 联系我们
        如有任何服务条款相关问题，请联系我们：
        邮箱：<EMAIL>

  exercise_analyses:
    success:
      created: "分析请求已提交，正在分析中"
      deleted: "分析记录已删除"
      analysis_completed: "分析完成"
      
    errors:
      not_found: "未找到该分析记录"
      create_failed: "创建分析请求失败"
      no_images_provided: "请上传至少一张运动数据图片"
      analysis_failed: "分析失败，请重试"
      too_many_requests: "分析请求过于频繁，请稍后再试"
      invalid_image_format: "图片格式不支持"
      
    status:
      pending: "待分析"
      analyzing: "分析中"
      completed: "已完成"
      failed: "分析失败"