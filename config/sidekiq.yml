# Sidekiq configuration
:verbose: false
:pidfile: ./tmp/pids/sidekiq.pid
:logfile: ./log/sidekiq.log
:concurrency: 5

# Redis configuration
:redis:
  url: <%= ENV.fetch('REDIS_URL', 'redis://localhost:6379/0') %>

# Queues configuration
:queues:
  - default
  - mailers
  - low

# Sidekiq-cron configuration
:cron:
  cleanup_expired_verification_codes:
    description: 'Clean up expired verification codes every hour'
    cron: '0 * * * *'
    class: 'CleanupExpiredVerificationCodesJob'
    queue: 'low'