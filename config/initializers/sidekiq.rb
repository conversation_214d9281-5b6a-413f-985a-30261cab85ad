require 'sidekiq'
require 'sidekiq-cron'

# Configure Sidekiq client
Sidekiq.configure_client do |config|
  config.redis = { 
    url: ENV.fetch('REDIS_URL', 'redis://localhost:6379/0')
  }
end

# Configure Sidekiq server
Sidekiq.configure_server do |config|
  config.redis = { 
    url: ENV.fetch('REDIS_URL', 'redis://localhost:6379/0')
  }
  
  # Load cron jobs from sidekiq.yml
  schedule_file = Rails.root.join('config', 'sidekiq.yml')
  if File.exist?(schedule_file)
    schedule = YAML.load_file(schedule_file)
    if schedule && schedule[:cron]
      schedule[:cron].each do |name, job_config|
        Sidekiq::Cron::Job.create(
          name: name.to_s,
          cron: job_config['cron'],
          class: job_config['class'],
          queue: job_config['queue'] || 'default'
        )
      end
    end
  end
end

# Sidekiq Web UI configuration
require 'sidekiq/web'
require 'sidekiq/cron/web'

# 生产环境下的基本认证（可选）
if Rails.env.production?
  Sidekiq::Web.use Rack::Auth::Basic do |username, password|
    username == ENV.fetch("SIDEKIQ_USERNAME", "admin") && 
    password == ENV.fetch("SIDEKIQ_PASSWORD", "changeme")
  end
end