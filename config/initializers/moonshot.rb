# Moonshot AI 配置
# 配置 Moonshot API 相关设置

Rails.application.config.moonshot = {
  # API 基础URL
  # 默认模型
  default_model: 'moonshot-v1-128k-vision-preview',
  
  # 模型配置
  model_config: {
    temperature: 0.7,
    max_tokens: 4000,
    top_p: 1.0,
    frequency_penalty: 0.0,
    presence_penalty: 0.0
  },
  
  # 定价配置（元/1000 tokens）
  pricing: {
    'moonshot-v1-128k-vision-preview' => {
      prompt: 0.012,
      completion: 0.012
    }
  },
  
  # 请求限制
  rate_limit: {
    requests_per_minute: 60,
    tokens_per_minute: 150000
  },
  
  # 超时设置
  timeout: {
    read: 30.seconds,
    write: 30.seconds,
    connect: 10.seconds
  }
}