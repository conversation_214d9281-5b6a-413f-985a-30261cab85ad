require "active_support/core_ext/integer/time"

Rails.application.configure do
  # Settings specified here will take precedence over those in config/application.rb.

  # Code is not reloaded between requests.
  config.enable_reloading = false

  # Eager load code on boot for better performance and memory savings (ignored by Rake tasks).
  config.eager_load = true

  # Full error reports are disabled.
  config.consider_all_requests_local = false

  # Cache assets for far-future expiry since they are all digest stamped.
  config.public_file_server.headers = { "cache-control" => "public, max-age=#{1.year.to_i}" }

  # Enable serving of images, stylesheets, and JavaScripts from an asset server.
  # config.asset_host = "http://assets.example.com"

  # Store uploaded files on the local file system (see config/storage.yml for options).
  config.active_storage.service = :local

  # Assume all access to the app is happening through a SSL-terminating reverse proxy.
  config.assume_ssl = true

  # Force all access to the app over SSL, use Strict-Transport-Security, and use secure cookies.
  config.force_ssl = true

  # Skip http-to-https redirect for the default health check endpoint.
  # config.ssl_options = { redirect: { exclude: ->(request) { request.path == "/up" } } }

  # Log to both STDOUT and file with rotation
  config.log_tags = [ :request_id ]
  
  # Configure log file path
  log_file_path = Rails.root.join('log', "production.log")
  
  # 使用更简洁的方式配置日志，同时输出到STDOUT和文件
  # 1. 创建文件日志，支持大小分割（100MB）
  file_logger = ActiveSupport::Logger.new(log_file_path, 7, 100 * 1024 * 1024)
    .tap { |logger| logger.formatter = config.log_formatter }
    .tap { |logger| logger.level = ActiveSupport::Logger.const_get(config.log_level.upcase) }
  
  # 2. 创建STDOUT日志
  stdout_logger = ActiveSupport::Logger.new(STDOUT)
    .tap { |logger| logger.formatter = config.log_formatter }
    .tap { |logger| logger.level = ActiveSupport::Logger.const_get(config.log_level.upcase) }
  
  # 3. 创建同时输出到文件和STDOUT的自定义日志设备
  multi_logger = Class.new(ActiveSupport::Logger) do
    def initialize(file_logger, stdout_logger)
      @file_logger = file_logger
      @stdout_logger = stdout_logger
    end
    
    %i(debug info warn error fatal unknown).each do |method_name|
      define_method(method_name) do |*args, &block|
        @file_logger.send(method_name, *args, &block)
        @stdout_logger.send(method_name, *args, &block)
      end
    end
    
    # 转发其他必要的方法
    def level=(level)
      @file_logger.level = level
      @stdout_logger.level = level
    end
    
    def level
      @file_logger.level
    end
    
    def formatter=(formatter)
      @file_logger.formatter = formatter
      @stdout_logger.formatter = formatter
    end
    
    def formatter
      @file_logger.formatter
    end
  end
  
  # 4. 设置最终的带标签日志器
  config.logger = ActiveSupport::TaggedLogging.new(multi_logger.new(file_logger, stdout_logger))
  
  # 注意：
  # 1. Ruby的Logger不原生支持同时按时间和大小分割日志
  # 2. 当前配置主要实现了按大小分割（100MB）
  # 3. 如需严格的按天分割，建议在生产环境中结合外部日志轮转工具如logrotate使用

  # Change to "debug" to log everything (including potentially personally-identifiable information!)
  config.log_level = ENV.fetch("RAILS_LOG_LEVEL", "info")

  # Prevent health checks from clogging up the logs.
  config.silence_healthcheck_path = "/up"

  # Don't log any deprecations.
  config.active_support.report_deprecations = false

  # Replace the default in-process memory cache store with a durable alternative.
  # Use Redis cache if REDIS_CACHE_URL is provided, otherwise use solid_cache_store
  if ENV['REDIS_CACHE_URL'].present?
    config.cache_store = :redis_cache_store, { url: ENV['REDIS_CACHE_URL'] }
  else
    config.cache_store = :solid_cache_store
  end

  # Replace the default in-process and non-durable queuing backend for Active Job.
  # config.active_job.queue_adapter = :solid_queue
  # config.solid_queue.connects_to = { database: { writing: :queue } }

  # 生产环境邮件配置 - 真实发送邮件
  config.action_mailer.raise_delivery_errors = true
  config.action_mailer.perform_deliveries = true
  config.action_mailer.delivery_method = :smtp

  # Set host to be used by links generated in mailer templates.
  config.action_mailer.default_url_options = { host: ENV.fetch('MAILER_HOST', 'xiancheng.online') }

  # SMTP 配置
  config.action_mailer.smtp_settings = {
    address: Settings.mailer_address,
    port: Settings.mailer_port,
    domain: Settings.mailer_domain,
    user_name: Settings.mailer_user_name,
    password: Settings.mailer_password,
    authentication: Settings.mailer_authentication,
    ssl: Settings.mailer_ssl,
    enable_starttls_auto: true
  }

  # Enable locale fallbacks for I18n (makes lookups for any locale fall back to
  # the I18n.default_locale when a translation cannot be found).
  config.i18n.fallbacks = true

  # Do not dump schema after migrations.
  config.active_record.dump_schema_after_migration = false

  # Only use :id for inspections in production.
  config.active_record.attributes_for_inspect = [ :id ]

  # Enable DNS rebinding protection and other `Host` header attacks.
  # config.hosts = [
  #   "example.com",     # Allow requests from example.com
  #   /.*\.example\.com/ # Allow requests from subdomains like `www.example.com`
  # ]
  #
  # Skip DNS rebinding protection for the default health check endpoint.
  # config.host_authorization = { exclude: ->(request) { request.path == "/up" } }
end
