Rails.application.routes.draw do
  mount Rswag::Ui::Engine => '/api-docs'
  mount Rswag::Api::Engine => '/api-docs'
  # Sidekiq Web UI
  require 'sidekiq/web'
  require 'sidekiq/cron/web'
  mount Sidekiq::Web => '/sidekiq'
  
  # Action Cable
  mount ActionCable.server => '/cable'
  
  # 认证相关路由
  scope '/auth' do
    post 'register', to: 'auth#register'
    post 'verify_email', to: 'auth#verify_email'
    post 'login', to: 'auth#login'
    delete 'logout', to: 'auth#logout'
    delete 'logout_all', to: 'auth#logout_all'
    post 'send_password_reset', to: 'auth#send_password_reset'
    post 'reset_password', to: 'auth#reset_password'
    post 'change_password', to: 'auth#change_password'
    get 'me', to: 'auth#me'
    get 'refresh', to: 'auth#refresh'
    delete 'delete_account', to: 'auth#delete_account'
  end
  
  # 用户管理路由
  resources :users do
    member do
      patch 'role', to: 'users#update_role'
      patch 'status', to: 'users#update_status'
      patch 'profile', to: 'users#update_profile'
      post 'reset_password', to: 'users#reset_password'
      post 'verify_email', to: 'users#verify_email'
      delete 'sessions', to: 'users#revoke_sessions'
      
      # 新的角色管理路由
      post 'roles/:role', to: 'users#add_role'
      delete 'roles/:role', to: 'users#remove_role'
    end
    
    collection do
      get 'statistics', to: 'users#statistics'
      get 'questionnaire_options', to: 'users#questionnaire_options'
    end
  end
  
  # 当前用户相关路由
  scope '/users/me' do
    patch 'profile', to: 'users#update_current_user_profile'
  end
  
  # 验证码相关路由
  post "verification_codes/send", to: "verification_codes#send_code"
  post "verification_codes/verify", to: "verification_codes#verify"
  
  # AI聊天相关路由
  scope '/ai_chat' do
    # 聊天室管理
    get 'rooms', to: 'ai_chat#rooms'
    post 'rooms', to: 'ai_chat#create_room'
    get 'rooms/:id', to: 'ai_chat#show'
    post 'rooms/:id/send_message', to: 'ai_chat#send_message'
    
    # 会话管理
    get 'sessions', to: 'ai_chat#sessions'
    get 'statistics', to: 'ai_chat#statistics'
    
    # 模型信息
    get 'models', to: 'ai_chat#models'
  end
  
  # 法律文档相关路由
  scope '/legal_documents' do
    get 'privacy_policy', to: 'legal_documents#privacy_policy'
    get 'terms_of_service', to: 'legal_documents#terms_of_service'
  end
  
  # 运动分析相关路由
  resources :exercise_analyses, only: [:index, :show, :create, :destroy] do
    # 可以在这里添加额外的运动分析相关路由
  end
  
  # Define your application routes per the DSL in https://guides.rubyonrails.org/routing.html

  # Reveal health status on /up that returns 200 if the app boots with no exceptions, otherwise 500.
  # Can be used by load balancers and uptime monitors to verify that the app is live.
  get "up" => "rails/health#show", as: :rails_health_check

  # Defines the root path route ("/")
  # root "posts#index"
end
