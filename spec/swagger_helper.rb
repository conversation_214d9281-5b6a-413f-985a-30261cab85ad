# frozen_string_literal: true

require 'rails_helper'

RSpec.configure do |config|
  # Specify a root folder where Swagger JSON files are generated
  # NOTE: If you're using the rswag-api to serve API descriptions, you'll need
  # to ensure that it's configured to serve Swagger from the same folder
  config.openapi_root = Rails.root.join('swagger').to_s

  # Define one or more Swagger documents and provide global metadata for each one
  # When you run the 'rswag:specs:swaggerize' rake task, the complete Swagger will
  # be generated at the provided relative path under openapi_root
  # By default, the operations defined in spec files are added to the first
  # document below. You can override this behavior by adding a openapi_spec tag to the
  # the root example_group in your specs, e.g. describe '...', openapi_spec: 'v2/swagger.json'
  config.openapi_specs = {
    'v1/swagger.yaml' => {
      openapi: '3.0.1',
      info: {
        title: 'Deep Sport API',
        version: 'v1',
        description: 'Deep Sport 运动应用后端API文档，包含用户管理、认证授权、AI聊天等功能',
        contact: {
          name: 'Deep Sport Team',
          email: '<EMAIL>'
        },
        license: {
          name: 'MIT'
        }
      },
      paths: {},
      # 全局服务端与全局安全要求
      servers: [
        {
          url: 'http://localhost:3000',
          description: '开发环境'
        },
        {
          url: 'https://api.deepsport.com',
          description: '生产环境'
        }
      ],
  # 全局要求：默认需要 Bearer 令牌，并支持通过 acceptLanguage 在 Authorize 中全局注入语言头
  # 如果某些公开接口不需要鉴权，可在对应的 rswag spec 中以 `security []` 或 `security [ { acceptLanguage: [] } ]` 覆盖
  security: [ { bearerAuth: [], acceptLanguage: [] } ],
      components: {
        securitySchemes: {
          bearerAuth: {
            type: 'http',
            scheme: 'bearer',
            # 这里使用的是会话令牌（Session Token），通过 Authorization: Bearer <token> 传递
            # 如后续切换为 JWT，可将 bearerFormat 调整为 JWT 并更新描述
            bearerFormat: 'token',
            description: '会话令牌认证，格式: Bearer <token>'
          },
          # 将 Accept-Language 作为一个 apiKey 类型的安全方案放到 header
          # 这样在 Swagger UI 的 Authorize 弹窗里可以全局输入，避免每个接口单独声明参数
          acceptLanguage: {
            type: 'apiKey',
            name: 'Accept-Language',
            in: 'header',
            description: '客户端语言偏好（如 zh-CN 或 en），将随所有请求发送'
          }
        },
        parameters: {
          AcceptLanguage: {
            name: 'Accept-Language',
            in: 'header',
            description: '客户端偏好的语言，支持: zh-CN(简体中文), en(英文)',
            required: false,
            schema: {
              type: 'string',
              enum: ['zh-CN', 'en', 'zh-CN,zh;q=0.9,en;q=0.8', 'en,zh-CN;q=0.9'],
              default: 'zh-CN'
            },
            example: 'zh-CN'
          },
          ContentType: {
            name: 'Content-Type',
            in: 'header',
            description: '请求内容类型',
            required: false,
            schema: {
              type: 'string',
              default: 'application/json'
            },
            example: 'application/json'
          }
        },
        schemas: {
          Error: {
            type: 'object',
            properties: {
              error: {
                type: 'string',
                description: '错误信息'
              },
              code: {
                type: 'integer',
                description: '错误代码'
              }
            },
            required: ['error']
          },
          User: {
            type: 'object',
            properties: {
              id: {
                type: 'integer',
                description: '用户ID'
              },
              email: {
                type: 'string',
                format: 'email',
                description: '用户邮箱'
              },
              name: {
                type: 'string',
                description: '用户姓名'
              },
              display_name: {
                type: 'string',
                description: '显示名称'
              },
              roles: {
                type: 'array',
                items: {
                  type: 'string',
                  enum: ['user', 'admin']
                },
                description: '用户角色数组'
              },
              primary_role: {
                type: 'string',
                enum: ['user', 'admin'],
                description: '主要角色（最高权限角色）'
              },
              roles_text: {
                type: 'string',
                description: '角色显示文本'
              },
              status: {
                type: 'string',
                enum: ['active', 'inactive', 'suspended'],
                description: '用户状态'
              },
              email_verified: {
                type: 'boolean',
                description: '邮箱是否已验证'
              },
              last_login_at: {
                type: 'string',
                format: 'date-time',
                description: '最后登录时间'
              },
              weight: {
                type: 'number',
                format: 'float',
                description: '体重(kg)'
              },
              height: {
                type: 'number',
                format: 'float',
                description: '身高(cm)'
              },
              birthday: {
                type: 'string',
                format: 'date',
                description: '生日'
              },
              gender: {
                type: 'string',
                enum: ['unknown', 'male', 'female'],
                description: '性别'
              },
              gender_text: {
                type: 'string',
                description: '性别文本'
              },
              age: {
                type: 'integer',
                description: '年龄'
              },
              bmi: {
                type: 'number',
                format: 'float',
                description: 'BMI指数'
              },
              bmi_status: {
                type: 'string',
                description: 'BMI状态'
              },
              exercise_frequency: {
                type: 'string',
                enum: ['occasionally', 'regular', 'athlete'],
                description: '运动频率'
              },
              exercise_frequency_text: {
                type: 'string',
                description: '运动频率文本'
              },
              fitness_goals: {
                type: 'array',
                items: {
                  type: 'string',
                  enum: ['weight_loss', 'muscle_gain', 'endurance', 'strength', 'flexibility', 'health', 'stress_relief', 'competition']
                },
                description: '健身目标数组'
              },
              obstacles: {
                type: 'array',
                items: {
                  type: 'string',
                  enum: ['lack_consistency', 'unhealthy_work_habits', 'lack_support', 'busy_schedule', 'lack_motivation']
                },
                description: '阻碍因素数组'
              },
              questionnaire_completed: {
                type: 'boolean',
                description: '问卷是否已完成'
              },
              created_at: {
                type: 'string',
                format: 'date-time',
                description: '创建时间'
              },
              updated_at: {
                type: 'string',
                format: 'date-time',
                description: '更新时间'
              }
            }
          },
          ValidationError: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: false },
              code: { type: 'integer', example: 422 },
              message: { type: 'string', example: '数据验证失败' },
              details: {
                type: 'object',
                additionalProperties: {
                  type: 'array',
                  items: { type: 'string' }
                },
                description: '具体的验证错误信息'
              },
              timestamp: { type: 'string', format: 'date-time' }
            }
          },
          ExerciseAnalysis: {
            type: 'object',
            properties: {
              id: {
                type: 'string',
                format: 'uuid',
                description: '运动分析记录ID'
              },
              user_id: {
                type: 'integer',
                description: '用户ID'
              },
              status: {
                type: 'string',
                enum: ['pending', 'analyzing', 'completed', 'failed'],
                description: '分析状态'
              },
              status_text: {
                type: 'string',
                description: '状态显示文本'
              },
              analysis_result: {
                type: 'object',
                description: '分析结果（JSON格式）',
                nullable: true
              },
              created_at: {
                type: 'string',
                format: 'date-time',
                description: '创建时间'
              },
              updated_at: {
                type: 'string',
                format: 'date-time',
                description: '更新时间'
              },
              image_urls: {
                type: 'array',
                items: {
                  type: 'string',
                  format: 'uri'
                },
                description: '上传的图片URL列表'
              }
            }
          },
          ExerciseAnalysisDetail: {
            type: 'object',
            properties: {
              overall_performance: {
                type: 'string',
                description: '整体表现评价'
              },
              pace_and_heart_rate_analysis: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    km: {
                      type: 'string',
                      description: '公里数'
                    },
                    pace: {
                      type: 'string',
                      description: '配速'
                    },
                    heart_rate: {
                      type: 'integer',
                      description: '心率'
                    }
                  }
                },
                description: '配速与心率分析（每公里）'
              },
              heart_rate_zone_distribution: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    zone: {
                      type: 'string',
                      description: '心率区间名称'
                    },
                    time: {
                      type: 'string',
                      description: '持续时间'
                    },
                    percentage: {
                      type: 'string',
                      description: '占比'
                    },
                    description: {
                      type: 'string',
                      description: '阶段描述'
                    }
                  }
                },
                description: '心率区间分布'
              },
              calorie_consumption_analysis: {
                type: 'object',
                properties: {
                  total_calories: {
                    type: 'integer',
                    description: '总消耗卡路里数字'
                  },
                  fat_burn: {
                    type: 'string',
                    description: '脂肪燃烧比例'
                  },
                  carbohydrate_burn: {
                    type: 'string',
                    description: '碳水化合物消耗比例'
                  },
                  metabolic_rate: {
                    type: 'string',
                    description: '代谢提升情况'
                  },
                  afterburn_effect: {
                    type: 'string',
                    description: '后燃效应'
                  }
                },
                description: '热量消耗与能量代谢分析'
              },
              cadence_and_running_efficiency: {
                type: 'object',
                properties: {
                  avg_cadence: {
                    type: 'string',
                    description: '平均步频'
                  },
                  stride_length: {
                    type: 'string',
                    description: '步幅长度'
                  },
                  efficiency: {
                    type: 'string',
                    description: '跑步效率'
                  },
                  improvement: {
                    type: 'string',
                    description: '与上周对比'
                  }
                },
                description: '步频与跑步效率'
              },
              environmental_factor_impact: {
                type: 'object',
                properties: {
                  temperature: {
                    type: 'string',
                    description: '温度'
                  },
                  humidity: {
                    type: 'string',
                    description: '湿度'
                  },
                  wind_speed: {
                    type: 'string',
                    nullable: true,
                    description: '风速（仅户外运动）'
                  },
                  equipment: {
                    type: 'string',
                    nullable: true,
                    description: '器械状态（仅室内运动）'
                  },
                  impact: {
                    type: 'string',
                    description: '环境影响评估'
                  }
                },
                description: '环境系数影响'
              },
              exercise_effect_and_physical_improvement: {
                type: 'object',
                properties: {
                  vo2max: {
                    type: 'string',
                    nullable: true,
                    description: '最大摄氧量（仅有氧运动）'
                  },
                  strength: {
                    type: 'string',
                    nullable: true,
                    description: '力量提升（仅力量训练）'
                  },
                  cardio_health: {
                    type: 'string',
                    description: '心肺健康'
                  },
                  muscle_endurance: {
                    type: 'string',
                    description: '肌肉耐力'
                  },
                  muscle_growth: {
                    type: 'string',
                    nullable: true,
                    description: '肌肉增长（仅力量训练）'
                  },
                  power_output: {
                    type: 'string',
                    nullable: true,
                    description: '力量输出（仅力量训练）'
                  },
                  recovery_time: {
                    type: 'string',
                    description: '恢复时间'
                  }
                },
                description: '运动效果与身体机能提升'
              },
              training_quality_evaluation_and_suggestions: {
                type: 'object',
                properties: {
                  score: {
                    type: 'integer',
                    description: '质量评分（0-100）'
                  },
                  strengths: {
                    type: 'array',
                    items: {
                      type: 'string'
                    },
                    description: '优势表现'
                  },
                  improvements: {
                    type: 'array',
                    items: {
                      type: 'string'
                    },
                    description: '改进建议'
                  },
                  next_training: {
                    type: 'string',
                    description: '下次训练建议'
                  }
                },
                description: '训练质量评价与建议'
              },
              weekly_plan_arrangement: {
                type: 'object',
                properties: {
                  monday: {
                    type: 'string',
                    description: '周一训练安排'
                  },
                  tuesday: {
                    type: 'string',
                    description: '周二训练安排'
                  },
                  wednesday: {
                    type: 'string',
                    description: '周三训练安排'
                  },
                  thursday: {
                    type: 'string',
                    description: '周四训练安排'
                  },
                  friday: {
                    type: 'string',
                    description: '周五训练安排'
                  },
                  saturday: {
                    type: 'string',
                    description: '周六训练安排'
                  },
                  sunday: {
                    type: 'string',
                    description: '周日训练安排'
                  }
                },
                description: '周计划安排'
              },
              nutrition_suggestions: {
                type: 'object',
                properties: {
                  pre_workout: {
                    type: 'string',
                    description: '训练前营养建议'
                  },
                  during_workout: {
                    type: 'string',
                    description: '训练中营养建议'
                  },
                  post_workout: {
                    type: 'string',
                    description: '训练后营养建议'
                  },
                  weekly_tips: {
                    type: 'array',
                    items: {
                      type: 'string'
                    },
                    description: '周营养重点'
                  }
                },
                description: '营养建议（一周）'
              },
              planned_vs_actual_execution: {
                type: 'object',
                properties: {
                  planned_distance: {
                    type: 'string',
                    nullable: true,
                    description: '计划距离（仅跑步/骑行）'
                  },
                  actual_distance: {
                    type: 'string',
                    nullable: true,
                    description: '实际距离（仅跑步/骑行）'
                  },
                  planned_time: {
                    type: 'string',
                    description: '计划时间'
                  },
                  actual_time: {
                    type: 'string',
                    description: '实际时间'
                  },
                  planned_sets: {
                    type: 'integer',
                    nullable: true,
                    description: '计划组数（仅力量训练）'
                  },
                  actual_sets: {
                    type: 'integer',
                    nullable: true,
                    description: '实际组数（仅力量训练）'
                  },
                  planned_weight: {
                    type: 'string',
                    nullable: true,
                    description: '计划重量（仅力量训练）'
                  },
                  actual_weight: {
                    type: 'string',
                    nullable: true,
                    description: '实际重量（仅力量训练）'
                  },
                  completion: {
                    type: 'string',
                    description: '完成度百分比'
                  }
                },
                description: '原计划VS实际执行'
              },
              exercise_performance_analysis: {
                type: 'object',
                properties: {
                  technique: {
                    type: 'string',
                    description: '技术分析'
                  },
                  consistency: {
                    type: 'string',
                    description: '一致性分析'
                  },
                  endurance: {
                    type: 'string',
                    description: '耐力分析'
                  },
                  speed: {
                    type: 'string',
                    nullable: true,
                    description: '速度分析（跑步）'
                  },
                  form: {
                    type: 'string',
                    nullable: true,
                    description: '动作质量（力量训练）'
                  },
                  progression: {
                    type: 'string',
                    nullable: true,
                    description: '进步情况（力量训练）'
                  }
                },
                description: '运动表现分析'
              }
            }
          }
        }
      }
    }
  }

  # Specify the format of the output Swagger file when running 'rswag:specs:swaggerize'.
  # The openapi_specs configuration option has the filename including format in
  # the key, this may want to be changed to avoid putting yaml in json files.
  # Defaults to json. Accepts ':json' and ':yaml'.
  config.openapi_format = :yaml
end
