# frozen_string_literal: true

require 'swagger_helper'

RSpec.describe 'Verification Codes API', type: :request do
  path '/verification_codes/send' do
    post '发送验证码' do
      tags '验证码管理'
      consumes 'application/json'
      produces 'application/json'
      description '向指定邮箱发送验证码'

      parameter name: :email, in: :query, type: :string, format: :email, required: true, description: '接收验证码的邮箱地址', example: '<EMAIL>'
      parameter name: :purpose, in: :query, type: :string, required: false, description: '验证码用途', example: 'email_verification', enum: ['email_verification', 'password_reset', 'account_verification']
      parameter name: :locale, in: :query, type: :string, required: false, description: '语言地区', example: 'zh-CN', enum: ['zh-CN', 'en']

      response '200', '验证码发送成功' do
        schema type: :object,
               properties: {
                 success: { type: :boolean, example: true },
                 message: { type: :string, example: '验证码已发送到您的邮箱，请查收' },
                 data: {
                   type: :object,
                   properties: {
                     email: { type: :string, format: :email, description: '邮箱地址' },
                     purpose: { type: :string, description: '验证码用途' },
                     expires_at: { type: :string, format: :'date-time', description: '过期时间' },
                     resend_after: { type: :string, format: :'date-time', description: '可重新发送时间' }
                   }
                 }
               }
        run_test!
      end

      response '400', '请求参数错误' do
        schema type: :object,
               properties: {
                 success: { type: :boolean, example: false },
                 error: { type: :string, example: '邮箱地址格式不正确' }
               }
        examples 'application/json' => {
          invalid_email: {
            summary: '邮箱格式错误',
            value: {
              success: false,
              error: '邮箱地址格式不正确'
            }
          },
          missing_email: {
            summary: '邮箱地址必填',
            value: {
              success: false,
              error: '邮箱地址不能为空'
            }
          }
        }
        run_test!
      end

      response '429', '发送频率过高' do
        schema type: :object,
               properties: {
                 success: { type: :boolean, example: false },
                 error: { type: :string, example: '验证码发送频率过高，请稍后再试' }
               }
        run_test!
      end

      response '500', '发送失败' do
        schema type: :object,
               properties: {
                 success: { type: :boolean, example: false },
                 error: { type: :string, example: '验证码发送失败，请稍后重试' }
               }
        run_test!
      end
    end
  end

  path '/verification_codes/verify' do
    post '验证验证码' do
      tags '验证码管理'
      consumes 'application/json'
      produces 'application/json'
      description '验证邮箱验证码是否正确'

      parameter name: :email, in: :query, type: :string, format: :email, required: true, description: '邮箱地址', example: '<EMAIL>'
      parameter name: :code, in: :query, type: :string, required: true, description: '验证码', example: '123456'
      parameter name: :purpose, in: :query, type: :string, required: false, description: '验证码用途', example: 'email_verification', enum: ['email_verification', 'password_reset', 'account_verification']
      parameter name: :locale, in: :query, type: :string, required: false, description: '语言地区', example: 'zh-CN', enum: ['zh-CN', 'en']

      response '200', '验证成功' do
        schema type: :object,
               properties: {
                 success: { type: :boolean, example: true },
                 message: { type: :string, example: '验证码验证成功' }
               }
        run_test!
      end

      response '400', '请求参数错误' do
        schema type: :object,
               properties: {
                 success: { type: :boolean, example: false },
                 error: { type: :string, example: '邮箱地址和验证码不能为空' }
               }
        run_test!
      end

      response '422', '验证失败' do
        schema type: :object,
               properties: {
                 success: { type: :boolean, example: false },
                 error: { type: :string, example: '验证码错误或已过期' }
               }
        examples 'application/json' => {
          invalid_code: {
            summary: '验证码错误',
            value: {
              success: false,
              error: '验证码错误'
            }
          },
          expired_code: {
            summary: '验证码已过期',
            value: {
              success: false,
              error: '验证码已过期，请重新发送'
            }
          },
          code_not_found: {
            summary: '验证码不存在',
            value: {
              success: false,
              error: '验证码不存在或已使用'
            }
          }
        }
        run_test!
      end
    end
  end
end