# 登录API功能测试

require 'rails_helper'

RSpec.describe 'Login API with Verification Code', type: :request do
  let(:user) { create(:user, email: '<EMAIL>', password: 'password123', email_verified: true) }
  let(:headers) { { 'Content-Type' => 'application/json' } }

  describe 'POST /auth/login' do
    context 'when using password login' do
      it 'logs in existing user with valid password' do
        post '/auth/login', params: {
          user: {
            email: user.email,
            password: 'password123'
          }
        }.to_json, headers: headers

        expect(response).to have_http_status(:ok)
        expect(json_response['success']).to be true
        expect(json_response['data']['user']['email']).to eq(user.email)
        expect(json_response['data']['token']).to be_present
      end

      it 'fails with invalid password' do
        post '/auth/login', params: {
          user: {
            email: user.email,
            password: 'wrongpassword'
          }
        }.to_json, headers: headers

        expect(response).to have_http_status(:unauthorized)
        expect(json_response['success']).to be false
        expect(json_response['message']).to eq('邮箱或密码错误')
      end
    end

    context 'when using verification code login' do
      before do
        # Mock verification code service
        allow(VerificationCodeService).to receive(:verify_code).and_return(
          ServiceResult.success(message: '验证成功')
        )
      end

      it 'logs in existing user with valid verification code' do
        post '/auth/login', params: {
          user: {
            email: user.email
          },
          verification_code: '123456'
        }.to_json, headers: headers

        expect(response).to have_http_status(:ok)
        expect(json_response['success']).to be true
        expect(json_response['data']['user']['email']).to eq(user.email)
        expect(json_response['data']['token']).to be_present
      end

      it 'auto-registers new user with valid verification code' do
        new_email = '<EMAIL>'
        
        post '/auth/login', params: {
          user: {
            email: new_email,
            name: '新用户',
            weight: 65.5,
            height: 175.0
          },
          verification_code: '123456'
        }.to_json, headers: headers

        expect(response).to have_http_status(:ok)
        expect(json_response['success']).to be true
        expect(json_response['data']['user']['email']).to eq(new_email)
        expect(json_response['data']['user']['name']).to eq('新用户')
        expect(json_response['data']['token']).to be_present
        
        # Verify user was created
        created_user = User.find_by(email: new_email)
        expect(created_user).to be_present
        expect(created_user.email_verified).to be true
      end

      it 'fails with invalid verification code' do
        allow(VerificationCodeService).to receive(:verify_code).and_return(
          ServiceResult.failure(message: '验证码无效或已过期')
        )

        post '/auth/login', params: {
          user: {
            email: '<EMAIL>'
          },
          verification_code: 'invalid'
        }.to_json, headers: headers

        expect(response).to have_http_status(:unauthorized)
        expect(json_response['success']).to be false
        expect(json_response['message']).to eq('验证码无效或已过期')
      end
    end

    context 'when neither password nor verification code provided' do
      it 'returns error' do
        post '/auth/login', params: {
          user: {
            email: '<EMAIL>'
          }
        }.to_json, headers: headers

        expect(response).to have_http_status(:unauthorized)
        expect(json_response['success']).to be false
        expect(json_response['message']).to eq('密码和验证码至少需要提供一个')
      end
    end

    context 'when auto-registration fails validation' do
      before do
        allow(VerificationCodeService).to receive(:verify_code).and_return(
          ServiceResult.success(message: '验证成功')
        )
      end

      it 'returns validation errors' do
        post '/auth/login', params: {
          user: {
            email: '<EMAIL>'
            # Missing required name field
          },
          verification_code: '123456'
        }.to_json, headers: headers

        expect(response).to have_http_status(:unprocessable_entity)
        expect(json_response['success']).to be false
        expect(json_response['message']).to eq('验证失败')
        expect(json_response['details']).to be_present
      end
    end

    context 'when both password and verification code provided' do
      before do
        allow(VerificationCodeService).to receive(:verify_code).and_return(
          ServiceResult.success(message: '验证成功')
        )
      end

      it 'prioritizes verification code over password' do
        post '/auth/login', params: {
          user: {
            email: user.email,
            password: 'password123'
          },
          verification_code: '123456'
        }.to_json, headers: headers

        expect(response).to have_http_status(:ok)
        expect(json_response['success']).to be true
        expect(VerificationCodeService).to have_received(:verify_code)
      end
    end
  end

  private

  def json_response
    JSON.parse(response.body)
  end
end