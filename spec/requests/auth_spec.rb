# frozen_string_literal: true

require 'swagger_helper'

RSpec.describe 'Auth API', type: :request do
  path '/auth/register' do
    post '用户注册' do
      tags '认证管理'
      consumes 'application/json'
      produces 'application/json'
      description '用户注册接口，支持邮箱注册，需要先发送邮箱验证码'
  # 覆盖全局鉴权：注册无需 Bearer
  security []

      parameter name: :user, in: :body, schema: {
        type: :object,
        properties: {
          user: {
            type: :object,
            properties: {
              email: { type: :string, format: :email, description: '用户邮箱', example: '<EMAIL>' },
              password: { type: :string, minLength: 6, description: '密码（至少6位）', example: 'password123' },
              name: { type: :string, description: '用户姓名', example: '张三' },
              weight: { type: :number, format: :float, description: '体重(kg)', example: 65.5, minimum: 1, maximum: 999 },
              height: { type: :number, format: :float, description: '身高(cm)', example: 175.0, minimum: 1, maximum: 299 },
              birthday: { type: :string, format: :date, description: '生日', example: '1990-01-01' },
              gender: { type: :integer, enum: [0, 1, 2], description: '性别：0-未知，1-男，2-女', example: 1 }
            },
            required: %w[email password name]
          },
          verification_code: { type: :string, description: '邮箱验证码', example: '123456' }
        },
        required: ['user', 'verification_code']
      }

      response '201', '注册成功' do
        schema type: :object,
               properties: {
                 success: { type: :boolean, example: true },
                 message: { type: :string, example: '注册成功，请查收邮箱验证邮件' },
                 data: {
                   type: :object,
                   properties: {
                     user: { '$ref' => '#/components/schemas/User' }
                   }
                 }
               }
        run_test!
      end

      response '422', '注册失败' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end
    end
  end

  path '/auth/verify_email' do
    post '邮箱验证' do
      tags '认证管理'
      consumes 'application/json'
      produces 'application/json'
      description '验证用户邮箱'
  # 覆盖全局鉴权：邮箱验证无需 Bearer
  security []

      parameter name: :email, in: :query, type: :string, format: :email, required: true, description: '用户邮箱'
      parameter name: :code, in: :query, type: :string, required: true, description: '验证码'

      response '200', '验证成功' do
        schema type: :object,
               properties: {
                 success: { type: :boolean, example: true },
                 message: { type: :string, example: '邮箱验证成功' }
               }
        run_test!
      end

      response '400', '验证失败' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end
    end
  end

  path '/auth/login' do
    post '用户登录或注册' do
      tags '认证管理'
      consumes 'application/json'
      produces 'application/json'
      description '用户登录接口，支持密码登录和验证码登录。如果用户不存在且提供验证码，将自动注册用户。密码和验证码至少需要提供一个。'
  # 覆盖全局鉴权：登录无需 Bearer
  security []

      parameter name: :credentials, in: :body, schema: {
        type: :object,
        properties: {
          user: {
            type: :object,
            properties: {
              email: { type: :string, format: :email, description: '用户邮箱', example: '<EMAIL>' },
              password: { type: :string, description: '密码（与验证码二选一）', example: 'password123' },
              name: { type: :string, description: '用户姓名（验证码登录时，用户不存在则自动注册需要）', example: '张三' },
              weight: { type: :number, format: :float, description: '体重(kg)（可选）', example: 65.5, minimum: 1, maximum: 999 },
              height: { type: :number, format: :float, description: '身高(cm)（可选）', example: 175.0, minimum: 1, maximum: 299 },
              birthday: { type: :string, format: :date, description: '生日（可选）', example: '1990-01-01' },
              gender: { type: :integer, enum: [0, 1, 2], description: '性别：0-未知，1-男，2-女（可选）', example: 1 }
            },
            required: %w[email]
          },
          verification_code: { type: :string, description: '邮箱验证码（与密码二选一）', example: '123456' }
        },
        required: ['user'],
        oneOf: [
          {
            properties: {
              user: {
                type: :object,
                properties: {
                  password: { type: :string, minLength: 1 }
                },
                required: ['password']
              }
            }
          },
          {
            properties: {
              verification_code: { type: :string, minLength: 1 }
            },
            required: ['verification_code']
          }
        ]
      }

      response '200', '登录成功' do
        schema type: :object,
               properties: {
                 success: { type: :boolean, example: true },
                 message: { type: :string, example: '登录成功' },
                 data: {
                   type: :object,
                   properties: {
                     user: { '$ref' => '#/components/schemas/User' },
                     token: { type: :string, description: 'JWT令牌', example: 'eyJhbGciOiJIUzI1NiJ9...' },
                     expires_at: { type: :string, format: :'date-time', description: '令牌过期时间' }
                   }
                 }
               }
        run_test!
      end

      response '401', '登录失败' do
        schema '$ref' => '#/components/schemas/Error'
        examples 'application/json' => {
          password_auth_failed: {
            summary: '密码认证失败',
            value: {
              success: false,
              code: 401,
              message: '邮箱或密码错误',
              timestamp: '2023-12-01T10:00:00Z'
            }
          },
          verification_code_failed: {
            summary: '验证码认证失败',
            value: {
              success: false,
              code: 401,
              message: '验证码无效或已过期',
              timestamp: '2023-12-01T10:00:00Z'
            }
          },
          missing_credentials: {
            summary: '缺少认证信息',
            value: {
              success: false,
              code: 401,
              message: '密码和验证码至少需要提供一个',
              timestamp: '2023-12-01T10:00:00Z'
            }
          }
        }
        run_test!
      end

      response '422', '参数验证失败（自动注册时）' do
        schema '$ref' => '#/components/schemas/Error'
        examples 'application/json' => {
          validation_failed: {
            summary: '自动注册验证失败',
            value: {
              success: false,
              code: 422,
              message: '验证失败',
              details: {
                name: ['姓名不能为空']
              },
              timestamp: '2023-12-01T10:00:00Z'
            }
          }
        }
        run_test!
      end
    end
  end

  path '/auth/logout' do
    delete '用户登出' do
      tags '认证管理'
      produces 'application/json'
      description '用户登出（当前设备）'
      security [bearerAuth: []]

      response '200', '登出成功' do
        schema type: :object,
               properties: {
                 success: { type: :boolean, example: true },
                 message: { type: :string, example: '登出成功' }
               }
        run_test!
      end

      response '401', '未授权' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end
    end
  end

  path '/auth/logout_all' do
    delete '全设备登出' do
      tags '认证管理'
      produces 'application/json'
      description '用户在所有设备上登出'
      security [bearerAuth: []]

      response '200', '全设备登出成功' do
        schema type: :object,
               properties: {
                 success: { type: :boolean, example: true },
                 message: { type: :string, example: '已从所有设备登出' }
               }
        run_test!
      end

      response '401', '未授权' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end
    end
  end

  path '/auth/send_password_reset' do
    post '发送重置密码邮件' do
      tags '认证管理'
      consumes 'application/json'
      produces 'application/json'
      description '发送密码重置邮件'
  # 覆盖全局鉴权：发送重置密码邮件无需 Bearer
  security []

      parameter name: :email, in: :query, type: :string, format: :email, required: true, description: '用户邮箱'

      response '200', '邮件发送成功' do
        schema type: :object,
               properties: {
                 success: { type: :boolean, example: true },
                 message: { type: :string, example: '如果邮箱存在，密码重置链接已发送' }
               }
        run_test!
      end
    end
  end

  path '/auth/reset_password' do
    post '重置密码' do
      tags '认证管理'
      consumes 'application/json'
      produces 'application/json'
      description '使用验证码重置密码'
  # 覆盖全局鉴权：重置密码无需 Bearer
  security []

      parameter name: :reset_data, in: :body, schema: {
        type: :object,
        properties: {
          email: { type: :string, format: :email, description: '用户邮箱', example: '<EMAIL>' },
          code: { type: :string, description: '验证码', example: '123456' },
          new_password: { type: :string, minLength: 6, description: '新密码', example: 'newpassword123' }
        },
        required: %w[email code new_password]
      }

      response '200', '密码重置成功' do
        schema type: :object,
               properties: {
                 success: { type: :boolean, example: true },
                 message: { type: :string, example: '密码重置成功' }
               }
        run_test!
      end

      response '422', '重置失败' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end
    end
  end

  path '/auth/change_password' do
    post '修改密码' do
      tags '认证管理'
      consumes 'application/json'
      produces 'application/json'
      description '修改当前用户密码'
      security [bearerAuth: []]

      parameter name: :password_data, in: :body, schema: {
        type: :object,
        properties: {
          current_password: { type: :string, description: '当前密码', example: 'oldpassword123' },
          new_password: { type: :string, minLength: 6, description: '新密码', example: 'newpassword123' }
        },
        required: %w[current_password new_password]
      }

      response '200', '密码修改成功' do
        schema type: :object,
               properties: {
                 success: { type: :boolean, example: true },
                 message: { type: :string, example: '密码修改成功' }
               }
        run_test!
      end

      response '422', '修改失败' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end

      response '401', '未授权' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end
    end
  end

  path '/auth/me' do
    get '获取当前用户信息' do
      tags '认证管理'
      produces 'application/json'
      description '获取当前登录用户的详细信息和会话列表'
      security [bearerAuth: []]

      response '200', '获取成功' do
        schema type: :object,
               properties: {
                 success: { type: :boolean, example: true },
                 data: {
                   type: :object,
                   properties: {
                     user: { '$ref' => '#/components/schemas/User' },
                     sessions: {
                       type: :array,
                       items: {
                         type: :object,
                         properties: {
                           id: { type: :integer, description: '会话ID' },
                           ip_address: { type: :string, description: 'IP地址' },
                           user_agent: { type: :string, description: '用户代理' },
                           created_at: { type: :string, format: :'date-time', description: '创建时间' },
                           expires_at: { type: :string, format: :'date-time', description: '过期时间' },
                           current: { type: :boolean, description: '是否为当前会话' }
                         }
                       }
                     }
                   }
                 }
               }
        run_test!
      end

      response '401', '未授权' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end
    end
  end

  path '/auth/refresh' do
    get '刷新会话' do
      tags '认证管理'
      produces 'application/json'
      description '延长当前会话的有效期'
      security [bearerAuth: []]

      response '200', '刷新成功' do
        schema type: :object,
               properties: {
                 success: { type: :boolean, example: true },
                 message: { type: :string, example: '会话已刷新' },
                 data: {
                   type: :object,
                   properties: {
                     expires_at: { type: :string, format: :'date-time', description: '新的过期时间' }
                   }
                 }
               }
        run_test!
      end

      response '401', '未授权' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end
    end
  end

  path '/auth/delete_account' do
    delete '删除账户' do
      tags '认证管理'
      consumes 'application/json'
      produces 'application/json'
      description '删除用户账户及所有相关数据，此操作不可恢复'
      security [Bearer: []]

      response '200', '账户删除成功' do
        schema type: :object,
               properties: {
                 success: { type: :boolean, example: true },
                 message: { type: :string, example: '账户删除成功，所有相关数据已被永久删除' }
               }
        run_test!
      end

      response '401', '未授权' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end
    end
  end
end