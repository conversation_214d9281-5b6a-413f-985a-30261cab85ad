# frozen_string_literal: true

require 'swagger_helper'

RSpec.describe 'Legal Documents API', type: :request do
  path '/legal_documents/privacy_policy' do
    get '获取隐私政策' do
      tags '法律文档'
      produces 'application/json'
      description '获取隐私政策内容，支持多语言'

      parameter name: :locale, in: :query, type: :string, required: false, 
                description: <<~DESC.strip,
                  语言设置，用于国际化支持：
                  * `zh-CN` - 中文（简体）
                  * `en` - English
                  
                  如果不指定，系统将按以下优先级自动选择：
                  1. URL 参数 locale
                  2. HTTP 头 Accept-Language
                  3. 默认语言 zh-CN
                DESC
                enum: ['zh-CN', 'en'],
                example: 'zh-CN'

      response '200', '获取成功' do
        schema type: :object,
               properties: {
                 success: { type: :boolean, example: true },
                 code: { type: :integer, example: 200 },
                 message: { type: :string, nullable: true, example: nil },
                 data: {
                   type: :object,
                   properties: {
                     title: { 
                       type: :string, 
                       description: '隐私政策标题', 
                       example: '隐私政策' 
                     },
                     content: { 
                       type: :string, 
                       description: '隐私政策内容（Markdown格式）', 
                       example: "# 隐私政策\n\n## 1. 信息收集\n我们收集以下类型的信息..." 
                     },
                     last_updated: { 
                       type: :string, 
                       description: '最后更新时间', 
                       example: '2025年8月19日' 
                     },
                     version: { 
                       type: :string, 
                       description: '版本号', 
                       example: '1.0' 
                     }
                   }
                 },
                 timestamp: { 
                   type: :string, 
                   format: 'date-time',
                   description: '响应时间戳'
                 }
               }

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data['success']).to be true
          expect(data['data']['title']).to eq('隐私政策')
        end
      end

      response '200', '获取成功（英文）' do
        let(:locale) { 'en' }
        
        schema type: :object,
               properties: {
                 success: { type: :boolean, example: true },
                 code: { type: :integer, example: 200 },
                 message: { type: :string, nullable: true, example: nil },
                 data: {
                   type: :object,
                   properties: {
                     title: { 
                       type: :string, 
                       description: '隐私政策标题', 
                       example: 'Privacy Policy' 
                     },
                     content: { 
                       type: :string, 
                       description: '隐私政策内容（Markdown格式）', 
                       example: "# Privacy Policy\n\n## 1. Information Collection\nWe collect the following types of information..." 
                     },
                     last_updated: { 
                       type: :string, 
                       description: '最后更新时间', 
                       example: 'August 19, 2025' 
                     },
                     version: { 
                       type: :string, 
                       description: '版本号', 
                       example: '1.0' 
                     }
                   }
                 },
                 timestamp: { 
                   type: :string, 
                   format: 'date-time',
                   description: '响应时间戳'
                 }
               }

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data['success']).to be true
          expect(data['data']['title']).to eq('Privacy Policy')
        end
      end
    end
  end

  path '/legal_documents/terms_of_service' do
    get '获取服务条款' do
      tags '法律文档'
      produces 'application/json'
      description '获取服务条款内容，支持多语言'

      parameter name: :locale, in: :query, type: :string, required: false, 
                description: <<~DESC.strip,
                  语言设置，用于国际化支持：
                  * `zh-CN` - 中文（简体）
                  * `en` - English
                  
                  如果不指定，系统将按以下优先级自动选择：
                  1. URL 参数 locale
                  2. HTTP 头 Accept-Language
                  3. 默认语言 zh-CN
                DESC
                enum: ['zh-CN', 'en'],
                example: 'zh-CN'

      response '200', '获取成功' do
        schema type: :object,
               properties: {
                 success: { type: :boolean, example: true },
                 code: { type: :integer, example: 200 },
                 message: { type: :string, nullable: true, example: nil },
                 data: {
                   type: :object,
                   properties: {
                     title: { 
                       type: :string, 
                       description: '服务条款标题', 
                       example: '服务条款' 
                     },
                     content: { 
                       type: :string, 
                       description: '服务条款内容（Markdown格式）', 
                       example: "# 服务条款\n\n## 1. 服务接受\n通过使用我们的服务，您同意遵守这些条款..." 
                     },
                     last_updated: { 
                       type: :string, 
                       description: '最后更新时间', 
                       example: '2025年8月19日' 
                     },
                     version: { 
                       type: :string, 
                       description: '版本号', 
                       example: '1.0' 
                     }
                   }
                 },
                 timestamp: { 
                   type: :string, 
                   format: 'date-time',
                   description: '响应时间戳'
                 }
               }

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data['success']).to be true
          expect(data['data']['title']).to eq('服务条款')
        end
      end

      response '200', '获取成功（英文）' do
        let(:locale) { 'en' }
        
        schema type: :object,
               properties: {
                 success: { type: :boolean, example: true },
                 code: { type: :integer, example: 200 },
                 message: { type: :string, nullable: true, example: nil },
                 data: {
                   type: :object,
                   properties: {
                     title: { 
                       type: :string, 
                       description: '服务条款标题', 
                       example: 'Terms of Service' 
                     },
                     content: { 
                       type: :string, 
                       description: '服务条款内容（Markdown格式）', 
                       example: "# Terms of Service\n\n## 1. Service Acceptance\nBy using our service, you agree to comply with these terms..." 
                     },
                     last_updated: { 
                       type: :string, 
                       description: '最后更新时间', 
                       example: 'August 19, 2025' 
                     },
                     version: { 
                       type: :string, 
                       description: '版本号', 
                       example: '1.0' 
                     }
                   }
                 },
                 timestamp: { 
                   type: :string, 
                   format: 'date-time',
                   description: '响应时间戳'
                 }
               }

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data['success']).to be true
          expect(data['data']['title']).to eq('Terms of Service')
        end
      end
    end
  end
end