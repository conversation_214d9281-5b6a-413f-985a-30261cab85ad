# frozen_string_literal: true

require 'swagger_helper'

RSpec.describe 'Users API', type: :request do
  path '/users' do
    get '获取用户列表' do
      tags '用户管理'
      produces 'application/json'
      description '获取系统中所有用户的列表（管理员权限）'
  # 继承全局 security（bearerAuth + acceptLanguage）

      parameter name: :search, in: :query, type: :string, required: false, description: '搜索关键词（邮箱、姓名）'
      parameter name: :role, in: :query, type: :string, enum: ['admin', 'user'], required: false, description: '按角色筛选'
      parameter name: :status, in: :query, type: :string, enum: ['active', 'inactive', 'suspended'], required: false, description: '按状态筛选'
      parameter name: :email_verified, in: :query, type: :boolean, required: false, description: '按邮箱验证状态筛选'
      parameter name: :gender, in: :query, type: :integer, enum: [0, 1, 2], required: false, description: '按性别筛选：0-未知，1-男，2-女'
      parameter name: :sort_by, in: :query, type: :string, required: false, description: '排序字段'
      parameter name: :page, in: :query, type: :integer, required: false, description: '页码'
      parameter name: :per_page, in: :query, type: :integer, required: false, description: '每页数量'

      response '200', '获取成功' do
        schema type: :object,
               properties: {
                 success: { type: :boolean, example: true },
                 data: {
                   type: :object,
                   properties: {
                     users: {
                       type: :array,
                       items: { '$ref' => '#/components/schemas/User' }
                     }
                   }
                 },
                 meta: {
                   type: :object,
                   properties: {
                     current_page: { type: :integer, example: 1 },
                     total_pages: { type: :integer, example: 10 },
                     total_count: { type: :integer, example: 100 },
                     per_page: { type: :integer, example: 10 }
                   }
                 }
               }
        run_test!
      end

      response '401', '未授权' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end

      response '403', '权限不足' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end
    end

    post '创建用户' do
      tags '用户管理'
      consumes 'application/json'
      produces 'application/json'
      description '创建新用户（管理员权限）'
  # 继承全局 security（bearerAuth + acceptLanguage）

      parameter name: :user, in: :body, schema: {
        type: :object,
        properties: {
          user: {
            type: :object,
            properties: {
              email: { type: :string, format: :email, description: '用户邮箱', example: '<EMAIL>' },
              name: { type: :string, description: '用户姓名', example: '新用户' },
              password: { type: :string, minLength: 6, description: '密码', example: 'password123' },
              roles: { 
                type: :array, 
                items: { type: :string, enum: ['admin', 'user'] },
                description: '用户角色数组', 
                example: ['user'] 
              },
              status: { type: :string, enum: ['active', 'inactive'], description: '用户状态', example: 'active' },
              skip_email_verification: { type: :boolean, description: '跳过邮箱验证', example: false },
              weight: { type: :number, format: :float, description: '体重(kg)', example: 65.5 },
              height: { type: :number, format: :float, description: '身高(cm)', example: 175.0 },
              birthday: { type: :string, format: :date, description: '生日', example: '1990-01-01' },
              gender: { type: :integer, enum: [0, 1, 2], description: '性别：0-未知，1-男，2-女', example: 1 }
            },
            required: %w[email name password]
          }
        },
        required: ['user']
      }

      response '201', '创建成功' do
        schema type: :object,
               properties: {
                 success: { type: :boolean, example: true },
                 message: { type: :string, example: '用户创建成功' },
                 data: {
                   type: :object,
                   properties: {
                     user: { '$ref' => '#/components/schemas/User' }
                   }
                 }
               }
        run_test!
      end

      response '422', '创建失败' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end

      response '401', '未授权' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end

      response '403', '权限不足' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end
    end
  end

  path '/users/{id}' do
    get '获取用户详情' do
      tags '用户管理'
      produces 'application/json'
      description '获取指定用户的详细信息'
  # 继承全局 security（bearerAuth + acceptLanguage）

      parameter name: :id, in: :path, type: :integer, required: true, description: '用户ID'

      response '200', '获取成功' do
        schema type: :object,
               properties: {
                 success: { type: :boolean, example: true },
                 data: {
                   type: :object,
                   properties: {
                     user: { '$ref' => '#/components/schemas/User' },
                     sessions: {
                       type: :array,
                       items: {
                         type: :object,
                         properties: {
                           id: { type: :integer, description: '会话ID' },
                           ip_address: { type: :string, description: 'IP地址' },
                           user_agent: { type: :string, description: '用户代理' },
                           created_at: { type: :string, format: :'date-time', description: '创建时间' },
                           expires_at: { type: :string, format: :'date-time', description: '过期时间' },
                           active: { type: :boolean, description: '是否活跃' }
                         }
                       }
                     }
                   }
                 }
               }
        run_test!
      end

      response '404', '用户不存在' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end

      response '401', '未授权' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end

      response '403', '权限不足' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end
    end

    patch '更新用户信息' do
      tags '用户管理'
      consumes 'application/json'
      produces 'application/json'
      description '更新用户信息'
  # 继承全局 security（bearerAuth + acceptLanguage）

      parameter name: :id, in: :path, type: :integer, required: true, description: '用户ID'
      parameter name: :user, in: :body, schema: {
        type: :object,
        properties: {
          user: {
            type: :object,
            properties: {
              email: { type: :string, format: :email, description: '用户邮箱' },
              name: { type: :string, description: '用户姓名' },
              weight: { type: :number, format: :float, description: '体重(kg)' },
              height: { type: :number, format: :float, description: '身高(cm)' },
              birthday: { type: :string, format: :date, description: '生日' },
              gender: { type: :integer, enum: [0, 1, 2], description: '性别：0-未知，1-男，2-女' }
            }
          }
        },
        required: ['user']
      }

      response '200', '更新成功' do
        schema type: :object,
               properties: {
                 success: { type: :boolean, example: true },
                 message: { type: :string, example: '用户信息更新成功' },
                 data: {
                   type: :object,
                   properties: {
                     user: { '$ref' => '#/components/schemas/User' }
                   }
                 }
               }
        run_test!
      end

      response '422', '更新失败' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end

      response '404', '用户不存在' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end

      response '401', '未授权' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end

      response '403', '权限不足' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end
    end

    delete '删除用户' do
      tags '用户管理'
      produces 'application/json'
      description '删除指定用户（管理员权限）'
  # 继承全局 security（bearerAuth + acceptLanguage）

      parameter name: :id, in: :path, type: :integer, required: true, description: '用户ID'

      response '200', '删除成功' do
        schema type: :object,
               properties: {
                 success: { type: :boolean, example: true },
                 message: { type: :string, example: '用户删除成功' }
               }
        run_test!
      end

      response '404', '用户不存在' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end

      response '401', '未授权' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end

      response '403', '权限不足' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end
    end
  end

  path '/users/{id}/profile' do
    patch '更新用户个人资料' do
      tags '用户管理'
      consumes 'application/json'
      produces 'application/json'
      description '更新用户个人资料信息（用户可更新自己的资料）'
  # 继承全局 security（bearerAuth + acceptLanguage）

      parameter name: :id, in: :path, type: :integer, required: true, description: '用户ID'
      parameter name: :user, in: :body, schema: {
        type: :object,
        properties: {
          user: {
            type: :object,
            properties: {
              name: { type: :string, description: '用户姓名', example: '张三' },
              weight: { type: :number, format: :float, description: '体重(kg)', example: 65.5, minimum: 1, maximum: 999 },
              height: { type: :number, format: :float, description: '身高(cm)', example: 175.0, minimum: 1, maximum: 299 },
              birthday: { type: :string, format: :date, description: '生日', example: '1990-01-01' },
              gender: { type: :integer, enum: [0, 1, 2], description: '性别：0-未知，1-男，2-女', example: 1 }
            }
          }
        },
        required: ['user']
      }

      response '200', '更新成功' do
        schema type: :object,
               properties: {
                 success: { type: :boolean, example: true },
                 message: { type: :string, example: '个人资料更新成功' },
                 data: {
                   type: :object,
                   properties: {
                     user: { '$ref' => '#/components/schemas/User' }
                   }
                 }
               }
        run_test!
      end

      response '422', '更新失败' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end

      response '404', '用户不存在' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end

      response '401', '未授权' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end

      response '403', '权限不足' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end
    end
  end

  path '/users/{id}/role' do
    patch '更新用户角色' do
      tags '用户管理'
      consumes 'application/json'
      produces 'application/json'
      description '更新用户角色（管理员权限）'
  # 继承全局 security（bearerAuth + acceptLanguage）

      parameter name: :id, in: :path, type: :integer, required: true, description: '用户ID'
      parameter name: :roles, in: :body, schema: {
        type: :object,
        properties: {
          roles: {
            type: :array,
            items: { type: :string, enum: ['admin', 'user'] },
            description: '新角色数组',
            example: ['user', 'admin']
          }
        },
        required: ['roles']
      }

      response '200', '角色更新成功' do
        schema type: :object,
               properties: {
                 success: { type: :boolean, example: true },
                 message: { type: :string, example: '用户角色更新成功' },
                 data: {
                   type: :object,
                   properties: {
                     user: { '$ref' => '#/components/schemas/User' }
                   }
                 }
               }
        run_test!
      end

      response '400', '角色无效' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end

      response '404', '用户不存在' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end

      response '401', '未授权' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end

      response '403', '权限不足' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end
    end
  end

  path '/users/{id}/roles/{role}' do
    post '添加用户角色' do
      tags '用户管理'
      produces 'application/json'
      description '为用户添加新角色（管理员权限）'
  # 继承全局 security（bearerAuth + acceptLanguage）

      parameter name: :id, in: :path, type: :integer, required: true, description: '用户ID'
      parameter name: :role, in: :path, type: :string, enum: ['admin', 'user'], required: true, description: '要添加的角色'

      response '200', '角色添加成功' do
        schema type: :object,
               properties: {
                 success: { type: :boolean, example: true },
                 message: { type: :string, example: '用户角色添加成功' },
                 data: {
                   type: :object,
                   properties: {
                     user: { '$ref' => '#/components/schemas/User' }
                   }
                 }
               }
        run_test!
      end

      response '400', '角色无效或已存在' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end

      response '404', '用户不存在' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end

      response '401', '未授权' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end

      response '403', '权限不足' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end
    end

    delete '移除用户角色' do
      tags '用户管理'
      produces 'application/json'
      description '移除用户角色（管理员权限）'
  # 继承全局 security（bearerAuth + acceptLanguage）

      parameter name: :id, in: :path, type: :integer, required: true, description: '用户ID'
      parameter name: :role, in: :path, type: :string, enum: ['admin', 'user'], required: true, description: '要移除的角色'

      response '200', '角色移除成功' do
        schema type: :object,
               properties: {
                 success: { type: :boolean, example: true },
                 message: { type: :string, example: '用户角色移除成功' },
                 data: {
                   type: :object,
                   properties: {
                     user: { '$ref' => '#/components/schemas/User' }
                   }
                 }
               }
        run_test!
      end

      response '400', '角色不存在或为最后一个角色' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end

      response '404', '用户不存在' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end

      response '401', '未授权' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end

      response '403', '权限不足' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end
    end
  end

  path '/users/{id}/status' do
    patch '更新用户状态' do
      tags '用户管理'
      consumes 'application/json'
      produces 'application/json'
      description '更新用户状态（管理员权限）'
  # 继承全局 security（bearerAuth + acceptLanguage）

      parameter name: :id, in: :path, type: :integer, required: true, description: '用户ID'
      parameter name: :status, in: :query, type: :string, enum: ['active', 'inactive', 'suspended'], required: true, description: '新状态'

      response '200', '状态更新成功' do
        schema type: :object,
               properties: {
                 success: { type: :boolean, example: true },
                 message: { type: :string, example: '用户状态更新成功' },
                 data: {
                   type: :object,
                   properties: {
                     user: { '$ref' => '#/components/schemas/User' }
                   }
                 }
               }
        run_test!
      end

      response '400', '状态无效' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end

      response '404', '用户不存在' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end

      response '401', '未授权' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end

      response '403', '权限不足' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end
    end
  end

  path '/users/{id}/reset_password' do
    post '重置用户密码' do
      tags '用户管理'
      consumes 'application/json'
      produces 'application/json'
      description '管理员重置用户密码'
  # 继承全局 security（bearerAuth + acceptLanguage）

      parameter name: :id, in: :path, type: :integer, required: true, description: '用户ID'
      parameter name: :new_password, in: :query, type: :string, required: true, description: '新密码'

      response '200', '密码重置成功' do
        schema type: :object,
               properties: {
                 success: { type: :boolean, example: true },
                 message: { type: :string, example: '密码重置成功' }
               }
        run_test!
      end

      response '422', '密码格式错误' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end

      response '404', '用户不存在' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end

      response '401', '未授权' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end

      response '403', '权限不足' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end
    end
  end

  path '/users/{id}/verify_email' do
    post '验证用户邮箱' do
      tags '用户管理'
      produces 'application/json'
      description '管理员验证用户邮箱'
      security [bearerAuth: []]

      parameter name: :id, in: :path, type: :integer, required: true, description: '用户ID'

      response '200', '邮箱验证成功' do
        schema type: :object,
               properties: {
                 success: { type: :boolean, example: true },
                 message: { type: :string, example: '邮箱验证成功' }
               }
        run_test!
      end

      response '404', '用户不存在' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end

      response '401', '未授权' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end

      response '403', '权限不足' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end
    end
  end

  path '/users/{id}/sessions' do
    delete '撤销用户会话' do
      tags '用户管理'
      produces 'application/json'
      description '撤销用户的所有会话（管理员权限）'
      security [bearerAuth: []]

      parameter name: :id, in: :path, type: :integer, required: true, description: '用户ID'

      response '200', '会话撤销成功' do
        schema type: :object,
               properties: {
                 success: { type: :boolean, example: true },
                 message: { type: :string, example: '用户会话已撤销' }
               }
        run_test!
      end

      response '404', '用户不存在' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end

      response '401', '未授权' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end

      response '403', '权限不足' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end
    end
  end

  path '/users/statistics' do
    get '获取用户统计信息' do
      tags '用户管理'
      produces 'application/json'
      description '获取用户统计数据（管理员权限）'
      security [bearerAuth: []]

      response '200', '获取成功' do
        schema type: :object,
               properties: {
                 success: { type: :boolean, example: true },
                 data: {
                   type: :object,
                   properties: {
                     total_users: { type: :integer, description: '总用户数', example: 1000 },
                     active_users: { type: :integer, description: '活跃用户数', example: 800 },
                     verified_users: { type: :integer, description: '已验证邮箱用户数', example: 900 },
                     admin_users: { type: :integer, description: '管理员用户数', example: 5 },
                     recent_registrations: { type: :integer, description: '最近7天注册用户数', example: 50 }
                   }
                 }
               }
        run_test!
      end

      response '401', '未授权' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end

      response '403', '权限不足' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end
    end
  end
end