require 'swagger_helper'

RSpec.describe 'Questionnaire API', type: :request do
  path '/questionnaire/options' do
    get 'Get questionnaire options' do
      tags 'Questionnaire'
      description 'Get available options for questionnaire fields (exercise frequency, fitness goals, obstacles)'
      produces 'application/json'
      
      response 200, 'Options retrieved successfully' do
        schema type: :object,
               properties: {
                 success: { type: :boolean, example: true },
                 code: { type: :integer, example: 200 },
                 message: { type: :string, nullable: true },
                 data: {
                   type: :object,
                   properties: {
                     exercise_frequencies: {
                       type: :array,
                       items: {
                         type: :object,
                         properties: {
                           value: { type: :string, example: 'occasionally' },
                           label: { type: :string, example: '偶尔锻炼(0-2次/周)' },
                           description: { type: :string, example: '0-2 times per week' }
                         }
                       }
                     },
                     fitness_goals: {
                       type: :array,
                       items: {
                         type: :object,
                         properties: {
                           value: { type: :string, example: 'weight_loss' },
                           label: { type: :string, example: '减肥瘦身' }
                         }
                       }
                     },
                     obstacles: {
                       type: :array,
                       items: {
                         type: :object,
                         properties: {
                           value: { type: :string, example: 'lack_consistency' },
                           label: { type: :string, example: '缺乏一致性' }
                         }
                       }
                     }
                   }
                 },
                 timestamp: { type: :string, format: :datetime }
               }

        run_test!
      end
    end
  end

  path '/questionnaire/submit' do
    post 'Submit questionnaire for authenticated user' do
      tags 'Questionnaire'
      description 'Submit or update questionnaire data for the currently authenticated user'
      consumes 'application/json'
      produces 'application/json'
      security [Bearer: []]
      
      parameter name: :questionnaire_data, in: :body, schema: {
        type: :object,
        properties: {
          exercise_frequency: { 
            type: :string, 
            enum: ['occasionally', 'regular', 'athlete'],
            description: 'Exercise frequency per week'
          },
          fitness_goals: { 
            type: :array, 
            items: { 
              type: :string,
              enum: ['weight_loss', 'muscle_gain', 'endurance', 'strength', 'flexibility', 'health', 'stress_relief', 'competition']
            },
            description: 'User fitness goals (can select multiple)'
          },
          obstacles: { 
            type: :array, 
            items: { 
              type: :string,
              enum: ['lack_consistency', 'unhealthy_work_habits', 'lack_support', 'busy_schedule', 'lack_motivation']
            },
            description: 'Obstacles preventing user from achieving goals (can select multiple)'
          }
        },
        required: ['exercise_frequency', 'fitness_goals', 'obstacles']
      }

      response 200, 'Questionnaire updated successfully' do
        schema type: :object,
               properties: {
                 success: { type: :boolean, example: true },
                 code: { type: :integer, example: 200 },
                 message: { type: :string, example: '问卷信息更新成功' },
                 data: {
                   type: :object,
                   properties: {
                     user: { '$ref' => '#/components/schemas/User' }
                   }
                 },
                 timestamp: { type: :string, format: :datetime }
               }

        run_test!
      end

      response 401, 'Unauthorized' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end

      response 422, 'Validation failed' do
        schema '$ref' => '#/components/schemas/ValidationError'
        run_test!
      end
    end
  end

  path '/questionnaire/submit_with_registration' do
    post 'Submit questionnaire with user registration' do
      tags 'Questionnaire'
      description 'Register new user and submit questionnaire data in one step. Used for the questionnaire-first registration flow.'
      consumes 'application/json'
      produces 'application/json'
      
      parameter name: :registration_data, in: :body, schema: {
        type: :object,
        properties: {
          email: { type: :string, format: :email, description: 'User email address' },
          verification_code: { type: :string, description: 'Email verification code' },
          name: { type: :string, description: 'User full name' },
          gender: { 
            type: :string, 
            enum: ['male', 'female', 'unknown'],
            description: 'User gender'
          },
          height: { type: :number, format: :decimal, description: 'Height in cm' },
          weight: { type: :number, format: :decimal, description: 'Weight in kg' },
          birthday: { type: :string, format: :date, description: 'Date of birth' },
          exercise_frequency: { 
            type: :string, 
            enum: ['occasionally', 'regular', 'athlete'],
            description: 'Exercise frequency per week'
          },
          fitness_goals: { 
            type: :array, 
            items: { 
              type: :string,
              enum: ['weight_loss', 'muscle_gain', 'endurance', 'strength', 'flexibility', 'health', 'stress_relief', 'competition']
            },
            description: 'User fitness goals (can select multiple)'
          },
          obstacles: { 
            type: :array, 
            items: { 
              type: :string,
              enum: ['lack_consistency', 'unhealthy_work_habits', 'lack_support', 'busy_schedule', 'lack_motivation']
            },
            description: 'Obstacles preventing user from achieving goals (can select multiple)'
          }
        },
        required: ['email', 'verification_code', 'name', 'exercise_frequency', 'fitness_goals', 'obstacles']
      }

      response 201, 'User registered and questionnaire submitted successfully' do
        schema type: :object,
               properties: {
                 success: { type: :boolean, example: true },
                 code: { type: :integer, example: 201 },
                 message: { type: :string, example: '注册成功，问卷信息已保存' },
                 data: {
                   type: :object,
                   properties: {
                     user: { '$ref' => '#/components/schemas/User' },
                     token: { type: :string, description: 'Authentication token' },
                     expires_at: { type: :string, format: :datetime, description: 'Token expiration time' }
                   }
                 },
                 timestamp: { type: :string, format: :datetime }
               }

        run_test!
      end

      response 422, 'Validation failed' do
        schema '$ref' => '#/components/schemas/ValidationError'
        run_test!
      end
    end
  end
end