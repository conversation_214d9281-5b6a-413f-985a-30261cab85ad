# frozen_string_literal: true

require 'swagger_helper'

RSpec.describe 'AI Chat API', type: :request do
  path '/ai_chat/rooms' do
    get '获取聊天室列表' do
      tags 'AI聊天'
      produces 'application/json'
      description '获取当前用户的AI聊天室列表'
  # 继承全局 security（bearerAuth + acceptLanguage）

      response '200', '获取成功' do
        schema type: :object,
               properties: {
                 success: { type: :boolean, example: true },
                 data: {
                   type: :object,
                   properties: {
                     rooms: {
                       type: :array,
                       items: {
                         type: :object,
                         properties: {
                           id: { type: :integer, description: '聊天室ID', example: 1 },
                           name: { type: :string, description: '聊天室名称', example: '技术讨论' },
                           description: { type: :string, description: '聊天室描述', example: '讨论技术相关话题' },
                           last_message: {
                             type: :object,
                             properties: {
                               id: { type: :integer, description: '最后消息ID' },
                               content: { type: :string, description: '最后消息内容' },
                               message_type: { type: :string, enum: ['user', 'assistant'], description: '消息类型' },
                               created_at: { type: :string, format: :'date-time', description: '创建时间' }
                             }
                           },
                           total_tokens: { type: :integer, description: '总token使用量', example: 1500 },
                           participant_count: { type: :integer, description: '参与者数量', example: 2 },
                           created_at: { type: :string, format: :'date-time', description: '创建时间' },
                           updated_at: { type: :string, format: :'date-time', description: '更新时间' }
                         }
                       }
                     }
                   }
                 }
               }
        run_test!
      end

      response '401', '未授权' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end
    end

    post '创建聊天室' do
      tags 'AI聊天'
      consumes 'application/json'
      produces 'application/json'
      description '创建新的AI聊天室'
  # 继承全局 security（bearerAuth + acceptLanguage）

      parameter name: :room, in: :body, schema: {
        type: :object,
        properties: {
          room: {
            type: :object,
            properties: {
              name: { type: :string, description: '聊天室名称', example: '新的讨论' },
              description: { type: :string, description: '聊天室描述', example: '关于新技术的讨论' }
            },
            required: %w[name]
          }
        },
        required: ['room']
      }

      response '201', '创建成功' do
        schema type: :object,
               properties: {
                 success: { type: :boolean, example: true },
                 message: { type: :string, example: '聊天室创建成功' },
                 data: {
                   type: :object,
                   properties: {
                     room: {
                       type: :object,
                       properties: {
                         id: { type: :integer, description: '聊天室ID' },
                         name: { type: :string, description: '聊天室名称' },
                         description: { type: :string, description: '聊天室描述' },
                         status: { type: :string, description: '聊天室状态' }
                       }
                     },
                     session: {
                       type: :object,
                       properties: {
                         id: { type: :integer, description: '会话ID' },
                         session_name: { type: :string, description: '会话名称' },
                         model_config: { type: :object, description: '模型配置' }
                       }
                     }
                   }
                 }
               }
        run_test!
      end

      response '422', '创建失败' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end

      response '401', '未授权' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end
    end
  end

  path '/ai_chat/rooms/{id}' do
    get '获取聊天室详情' do
      tags 'AI聊天'
      produces 'application/json'
      description '获取指定聊天室的详情和消息历史'
  # 继承全局 security（bearerAuth + acceptLanguage）

      parameter name: :id, in: :path, type: :integer, required: true, description: '聊天室ID'

      response '200', '获取成功' do
        schema type: :object,
               properties: {
                 success: { type: :boolean, example: true },
                 data: {
                   type: :object,
                   properties: {
                     room: {
                       type: :object,
                       properties: {
                         id: { type: :integer, description: '聊天室ID' },
                         name: { type: :string, description: '聊天室名称' },
                         description: { type: :string, description: '聊天室描述' },
                         status: { type: :string, description: '聊天室状态' }
                       }
                     },
                     messages: {
                       type: :array,
                       items: {
                         type: :object,
                         properties: {
                           id: { type: :integer, description: '消息ID' },
                           content: { type: :string, description: '消息内容' },
                           message_type: { type: :string, enum: ['user', 'assistant'], description: '消息类型' },
                           user: {
                             type: :object,
                             properties: {
                               id: { type: :integer, description: '用户ID' },
                               name: { type: :string, description: '用户名称' },
                               email: { type: :string, description: '用户邮箱' }
                             }
                           },
                           token_count: { type: :integer, description: 'Token数量' },
                           model_name: { type: :string, description: 'AI模型名称' },
                           parent_message_id: { type: :integer, description: '父消息ID' },
                           status: { type: :string, description: '消息状态' },
                           created_at: { type: :string, format: :'date-time', description: '创建时间' }
                         }
                       }
                     },
                     total_tokens: { type: :integer, description: '总token使用量' }
                   }
                 }
               }
        run_test!
      end

      response '404', '聊天室不存在' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end

      response '403', '权限不足' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end

      response '401', '未授权' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end
    end
  end

  path '/ai_chat/rooms/{id}/send_message' do
    post '发送消息' do
      tags 'AI聊天'
      consumes 'application/json'
      produces 'application/json'
      description '向AI聊天室发送消息'
  # 继承全局 security（bearerAuth + acceptLanguage）

      parameter name: :id, in: :path, type: :integer, required: true, description: '聊天室ID'
      parameter name: :message, in: :query, type: :string, required: true, description: '消息内容', example: '你好，请介绍一下深度学习'

      response '201', '消息发送成功' do
        schema type: :object,
               properties: {
                 success: { type: :boolean, example: true },
                 message: { type: :string, example: '消息发送成功，AI正在处理中' },
                 data: {
                   type: :object,
                   properties: {
                     user_message: {
                       type: :object,
                       properties: {
                         id: { type: :integer, description: '消息ID' },
                         content: { type: :string, description: '消息内容' },
                         message_type: { type: :string, description: '消息类型' },
                         created_at: { type: :string, format: :'date-time', description: '创建时间' }
                       }
                     }
                   }
                 }
               }
        run_test!
      end

      response '400', '消息内容为空' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end

      response '404', '聊天室不存在' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end

      response '403', '权限不足' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end

      response '401', '未授权' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end
    end
  end

  path '/ai_chat/sessions' do
    get '获取AI聊天会话列表' do
      tags 'AI聊天'
      produces 'application/json'
      description '获取当前用户的AI聊天会话列表'
  # 继承全局 security（bearerAuth + acceptLanguage）

      response '200', '获取成功' do
        schema type: :object,
               properties: {
                 success: { type: :boolean, example: true },
                 data: {
                   type: :object,
                   properties: {
                     sessions: {
                       type: :array,
                       items: {
                         type: :object,
                         properties: {
                           id: { type: :integer, description: '会话ID' },
                           session_name: { type: :string, description: '会话名称' },
                           chat_room: {
                             type: :object,
                             properties: {
                               id: { type: :integer, description: '聊天室ID' },
                               name: { type: :string, description: '聊天室名称' }
                             }
                           },
                           total_tokens: { type: :integer, description: '总token数' },
                           total_prompt_tokens: { type: :integer, description: '提示token数' },
                           total_completion_tokens: { type: :integer, description: '完成token数' },
                           last_message_at: { type: :string, format: :'date-time', description: '最后消息时间' },
                           created_at: { type: :string, format: :'date-time', description: '创建时间' },
                           cost_estimate: { type: :number, format: :float, description: '预估费用' }
                         }
                       }
                     }
                   }
                 }
               }
        run_test!
      end

      response '401', '未授权' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end
    end
  end

  path '/ai_chat/statistics' do
    get '获取AI聊天统计信息' do
      tags 'AI聊天'
      produces 'application/json'
      description '获取当前用户的AI聊天统计数据'
  # 继承全局 security（bearerAuth + acceptLanguage）

      response '200', '获取成功' do
        schema type: :object,
               properties: {
                 success: { type: :boolean, example: true },
                 data: {
                   type: :object,
                   properties: {
                     statistics: {
                       type: :object,
                       properties: {
                         total_rooms: { type: :integer, description: '总聊天室数', example: 5 },
                         total_sessions: { type: :integer, description: '总会话数', example: 8 },
                         total_messages: { type: :integer, description: '总消息数', example: 150 },
                         total_tokens: { type: :integer, description: '总token使用量', example: 15000 },
                         total_prompt_tokens: { type: :integer, description: '总提示token数', example: 8000 },
                         total_completion_tokens: { type: :integer, description: '总完成token数', example: 7000 },
                         last_chat_at: { type: :string, format: :'date-time', description: '最后聊天时间' }
                       }
                     }
                   }
                 }
               }
        run_test!
      end

      response '401', '未授权' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end
    end
  end

  path '/ai_chat/models' do
    get '获取可用AI模型列表' do
      tags 'AI聊天'
      produces 'application/json'
      description '获取系统支持的AI模型列表'
  # 继承全局 security（bearerAuth + acceptLanguage）

      response '200', '获取成功' do
        schema type: :object,
               properties: {
                 success: { type: :boolean, example: true },
                 data: {
                   type: :object,
                   properties: {
                     models: {
                       type: :array,
                       items: {
                         type: :object,
                         properties: {
                           id: { type: :string, description: '模型ID', example: 'moonshot-v1-8k' },
                           name: { type: :string, description: '模型名称', example: 'Moonshot v1 8K' },
                           description: { type: :string, description: '模型描述' },
                           context_length: { type: :integer, description: '上下文长度', example: 8192 },
                           input_price: { type: :number, format: :float, description: '输入价格/千token' },
                           output_price: { type: :number, format: :float, description: '输出价格/千token' }
                         }
                       }
                     }
                   }
                 }
               }
        run_test!
      end

      response '401', '未授权' do
        schema '$ref' => '#/components/schemas/Error'
        run_test!
      end
    end
  end
end