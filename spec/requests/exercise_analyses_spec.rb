require 'rails_helper'

RSpec.describe 'ExerciseAnalyses API', type: :request do
  let(:user) { create(:user) }
  let(:auth_headers) { { 'Authorization' => "Bearer #{user.generate_jwt}" } }

  # 运动分析记录的常见参数
  let(:valid_analysis_params) {
    {
      exercise_analysis: {
        # 文件上传部分在测试中会单独处理
      }
    }
  }

  describe 'GET /exercise_analyses' do
    it 'returns all exercise analyses for the current user with HTTP status 200' do
      # 创建一些运动分析记录
      create(:exercise_analysis, user: user, status: :completed)
      create(:exercise_analysis, user: user, status: :analyzing)
      
      # 创建属于其他用户的记录（不应该返回）
      create(:exercise_analysis, status: :completed)

      # 发送请求
      get '/exercise_analyses', headers: auth_headers

      # 验证响应
      expect(response).to have_http_status(:ok)
      expect(JSON.parse(response.body)['data'].length).to eq(2)
    end

    it_behaves_like 'a secured endpoint', :get, '/exercise_analyses'
  end

  describe 'GET /exercise_analyses/:id' do
    let(:exercise_analysis) { create(:exercise_analysis, user: user, status: :completed) }
    let(:other_analysis) { create(:exercise_analysis, status: :completed) }

    it 'returns a specific exercise analysis for the current user with HTTP status 200' do
      # 发送请求
      get "/exercise_analyses/#{exercise_analysis.id}", headers: auth_headers

      # 验证响应
      expect(response).to have_http_status(:ok)
      expect(JSON.parse(response.body)['data']['id']).to eq(exercise_analysis.id)
    end

    it 'returns HTTP status 404 when accessing another user\'s analysis' do
      # 发送请求
      get "/exercise_analyses/#{other_analysis.id}", headers: auth_headers

      # 验证响应
      expect(response).to have_http_status(:not_found)
    end

    it_behaves_like 'a secured endpoint', :get, "/exercise_analyses/#{SecureRandom.uuid}"
  end

  describe 'POST /exercise_analyses' do
    # 注意：这个测试仅验证基本流程，实际的文件上传需要更复杂的测试设置
    it 'creates a new exercise analysis with HTTP status 201 when valid parameters are provided' do
      # 在实际测试中，需要使用fixture_file_upload来上传文件
      # 这里简化处理，只验证基本流程

      # 发送请求（简化版）
      post '/exercise_analyses', headers: auth_headers.merge({'Content-Type' => 'multipart/form-data'})

      # 验证响应（实际测试中需要更精确的验证）
      expect(response).to have_http_status(:created)
    end

    it_behaves_like 'a secured endpoint', :post, '/exercise_analyses'
  end

  describe 'DELETE /exercise_analyses/:id' do
    let(:exercise_analysis) { create(:exercise_analysis, user: user) }
    let(:other_analysis) { create(:exercise_analysis) }

    it 'deletes a specific exercise analysis for the current user with HTTP status 204' do
      # 发送请求
      delete "/exercise_analyses/#{exercise_analysis.id}", headers: auth_headers

      # 验证响应
      expect(response).to have_http_status(:no_content)
      expect(ExerciseAnalysis.find_by(id: exercise_analysis.id)).to be_nil
    end

    it 'returns HTTP status 404 when trying to delete another user\'s analysis' do
      # 发送请求
      delete "/exercise_analyses/#{other_analysis.id}", headers: auth_headers

      # 验证响应
      expect(response).to have_http_status(:not_found)
      expect(ExerciseAnalysis.find_by(id: other_analysis.id)).not_to be_nil
    end

    it_behaves_like 'a secured endpoint', :delete, "/exercise_analyses/#{SecureRandom.uuid}"
  end
end