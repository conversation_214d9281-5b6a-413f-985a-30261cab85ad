require 'rails_helper'

RSpec.describe LegalDocumentsController, type: :controller do
  describe 'GET #privacy_policy' do
    it '返回隐私政策内容（中文）' do
      get :privacy_policy
      
      expect(response).to have_http_status(:ok)
      
      json_response = JSON.parse(response.body)
      expect(json_response['success']).to be true
      expect(json_response['data']).to have_key('title')
      expect(json_response['data']).to have_key('content')
      expect(json_response['data']).to have_key('last_updated')
      expect(json_response['data']).to have_key('version')
      expect(json_response['data']['title']).to eq('隐私政策')
    end
    
    it '返回隐私政策内容（英文）' do
      get :privacy_policy, params: { locale: 'en' }
      
      expect(response).to have_http_status(:ok)
      
      json_response = JSON.parse(response.body)
      expect(json_response['success']).to be true
      expect(json_response['data']['title']).to eq('Privacy Policy')
    end
    
    it '通过Accept-Language头设置语言' do
      request.headers['Accept-Language'] = 'en-US,en;q=0.9'
      get :privacy_policy
      
      expect(response).to have_http_status(:ok)
      
      json_response = JSON.parse(response.body)
      expect(json_response['success']).to be true
      expect(json_response['data']['title']).to eq('Privacy Policy')
    end
  end
  
  describe 'GET #terms_of_service' do
    it '返回服务条款内容（中文）' do
      get :terms_of_service
      
      expect(response).to have_http_status(:ok)
      
      json_response = JSON.parse(response.body)
      expect(json_response['success']).to be true
      expect(json_response['data']).to have_key('title')
      expect(json_response['data']).to have_key('content')
      expect(json_response['data']).to have_key('last_updated')
      expect(json_response['data']).to have_key('version')
      expect(json_response['data']['title']).to eq('服务条款')
    end
    
    it '返回服务条款内容（英文）' do
      get :terms_of_service, params: { locale: 'en' }
      
      expect(response).to have_http_status(:ok)
      
      json_response = JSON.parse(response.body)
      expect(json_response['success']).to be true
      expect(json_response['data']['title']).to eq('Terms of Service')
    end
    
    it '通过Accept-Language头设置语言' do
      request.headers['Accept-Language'] = 'en-US,en;q=0.9'
      get :terms_of_service
      
      expect(response).to have_http_status(:ok)
      
      json_response = JSON.parse(response.body)
      expect(json_response['success']).to be true
      expect(json_response['data']['title']).to eq('Terms of Service')
    end
  end
end