###

# curl -X POST http://localhost:3000/verification_codes/send
#  -H "Content-Type: application/json"
#  -d '{"email":"<EMAIL>","locale":"zh-CN"}'
POST http://localhost:3000/verification_codes/send
Content-Type: application/json

{
  "email": "<EMAIL>",
  "locale": "zh-CN"
}

###
WEBSOCKET ws://localhost:3000/cable?token=Qe7TPcyIWyIUnb4aUlYH16wT_HfYzw6GiF4giLBweZM
Origin: http://localhost:3000
###
GET  x
Content-Type: application/json

{
    "command":"subscribed",
    "identifier":"{\"channel\":\"AiChatChannel\",\"chat_room_id\":\"38\"}"
}

### 登录
POST http://localhost:3000/auth/login
Content-Type: application/json
Accept-Language: en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7

{
  "user":{
  "email": "<EMAIL>",
  "password": "admin123456"
}
}

###
GET http://localhost:3000/legal_documents/privacy_policy
Content-Type: application/json
Accept-Language: en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7
###
# curl -X 'GET'
#  'http://localhost:3000/auth/me'
#  -H 'accept: application/json'
#  -H 'Authorization: Bearer jCivQpK-bQ2TZmPDSaqZD9n9UMcgosfChb4RGmh4j60'
GET http://localhost:3000/auth/me
accept: application/json
Authorization: Bearer jCivQpK-bQ2TZmPDSaqZD9n9UMcgosfChb4RGmh4j60
Accept-Language: zh-CN,zh;q=0.9,en;q=0.8

###
POST http://localhost:3000/auth/login
Content-Type: application/json

{
  "user": {
    "email": "<EMAIL>"
  },
  "verification_code": "990388"
}

###
POST {{base_url}}/verification_codes/send
Content-Type: application/json

{
  "email": "<EMAIL>"
}

###
GET {{base_url}}/users
Authorization: Bearer {{token}}
###
GET {{base_url}}/exercise_analyses
Authorization: Bearer {{token}}
###
GET {{base_url}}/exercise_analyses/23
Authorization: Bearer {{token}}
###
POST {{base_url}}/verification_codes/send
Content-Type: application/json

{
  "email": "<EMAIL>"
}
###
POST {{base_url}} /auth/login
Content-Type: application/json

{
  "user": {
    "email": "<EMAIL>"
  },
  "verification_code": "555555"
}