# Redis 配置示例
# Sidekiq 使用的 Redis URL
REDIS_URL=redis://localhost:6379/0

# Rails Cache 使用的 Redis URL（可选，如果不设置则使用默认的缓存存储）
REDIS_CACHE_URL=redis://localhost:6379/1

# Sidekiq Web UI 认证（生产环境）
SIDEKIQ_USERNAME=admin
SIDEKIQ_PASSWORD=changeme

# 其他可能的 Redis 配置示例：
# REDIS_URL=redis://username:password@hostname:port/database
# REDIS_CACHE_URL=redis://username:password@hostname:port/database

# 如果使用 Redis Sentinel:
# REDIS_URL=redis://sentinel1:26379,sentinel2:26379,sentinel3:26379/mymaster
# REDIS_CACHE_URL=redis://sentinel1:26379,sentinel2:26379,sentinel3:26379/mymaster

# 如果使用 Redis Cluster:
# REDIS_URL=redis://node1:7000,node2:7000,node3:7000
# REDIS_CACHE_URL=redis://node1:7000,node2:7000,node3:7000