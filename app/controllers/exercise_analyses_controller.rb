# 运动分析控制器
class ExerciseAnalysesController < ApplicationController
  before_action :authenticate_user!

  # GET /api/exercise_analyses
  # 获取用户的运动分析历史列表
  def index
    service = ExerciseAnalysisService.new
    result = service.get_analysis_history(current_user, params)
    if result.success?
      render_success(
        data: {
          analyses: result.data[:analyses].map { |analysis| analysis_response(analysis) }
        },
        meta: result.meta
      )
    else
      render_error(message: result.message)
    end
  end

  # GET /api/exercise_analyses/:id
  # 获取单个运动分析的详细结果
  def show
    service = ExerciseAnalysisService.new
    result = service.get_analysis_detail(current_user, params[:id])
    if result.success?
      render_success(
        data: {
          analysis: analysis_detail_response(result.data)
        }
      )
    else
      render_not_found(
        message: I18n.t("exercise_analyses.errors.not_found")
      )
    end
  end

  # POST /api/exercise_analyses
  # 上传运动数据图片并创建分析请求
  def create
    # 检查是否有上传的文件
    unless params[:images].present?
      render_validation_error(
        { base: [I18n.t("exercise_analyses.errors.no_images_provided")] },
        message: I18n.t("exercise_analyses.errors.create_failed")
      )
      return
    end

    service = ExerciseAnalysisService.new
    result = service.create_analysis(current_user, params[:images], params)
    if result.success?
      render_success(
        data: {
          analysis: analysis_response(result.data)
        },
        message: I18n.t("exercise_analyses.success.created"),
        status: :created
      )
    else
      render_validation_error(
        { base: [result.message] },
        message: I18n.t("exercise_analyses.errors.create_failed")
      )
    end
  end

  # DELETE /api/exercise_analyses/:id
  # 删除运动分析记录
  def destroy
    service = ExerciseAnalysisService.new
    result = service.delete_analysis(current_user, params[:id])
    if result.success?
      render_success(
        message: I18n.t("exercise_analyses.success.deleted")
      )
    else
      render_not_found(
        message: I18n.t("exercise_analyses.errors.not_found")
      )
    end
  end

  private

  # 基础的分析响应数据
  def analysis_response(analysis)
    {
      id: analysis.id,
      status: analysis.status,
      created_at: analysis.created_at.iso8601,
      updated_at: analysis.updated_at.iso8601,
      image_count: analysis.images.count,
      type: analysis.completed? ? analysis.analysis_result["type"] : analysis.status,
      score: analysis.completed? ? analysis.analysis_result["score"] : nil,
      has_result: analysis.analysis_result.present? && analysis.completed?
    }
  end

  # 详细的分析响应数据（包含结果）
  def analysis_detail_response(analysis)
    response = analysis_response(analysis)
    response.merge!(
      image_urls: analysis.image_urls,
      analysis_result: analysis.completed? ? analysis.analysis_result : nil,
      # 各个分析部分的详细数据
      overall_performance: analysis.overall_performance,
      pace_and_heart_rate_analysis: analysis.pace_and_heart_rate_analysis,
      heart_rate_zone_distribution: analysis.heart_rate_zone_distribution,
      calorie_consumption_analysis: analysis.calorie_consumption_analysis,
      cadence_and_running_efficiency: analysis.cadence_and_running_efficiency,
      environmental_factor_impact: analysis.environmental_factor_impact,
      exercise_effect_and_physical_improvement: analysis.exercise_effect_and_physical_improvement,
      training_quality_evaluation_and_suggestions: analysis.training_quality_evaluation_and_suggestions,
      weekly_plan_arrangement: analysis.weekly_plan_arrangement,
      nutrition_suggestions: analysis.nutrition_suggestions,
      planned_vs_actual_execution: analysis.planned_vs_actual_execution,
      exercise_performance_analysis: analysis.exercise_performance_analysis
    )
  end
end