class AuthController < ApplicationController
  skip_before_action :authenticate_user!, only: [:register, :verify_email, :login, :send_password_reset, :reset_password]

  # POST /auth/register
  def register
    # 先验证邮箱验证码
    verification_result = VerificationCodeService.verify_code(
      registration_params[:email],
      params[:verification_code],
      'email_verification'
    )

    unless verification_result.success?
      return render_error(
        message: verification_result.message,
        status: :unprocessable_content
      )
    end

    service = AuthenticationService.new
    result = service.register(registration_params.merge(email_verified: true),
                              ip_address: client_ip,
                              user_agent: user_agent)

    if result.success?
      log_user_activity('registered', result.data[:user])
      render_success(
        data: result.data,
        message: result.message,
        status: :created
      )
    else
      render_service_result(result, error_status: :unprocessable_content)
    end
  end

  # POST /auth/verify_email
  def verify_email
    service = AuthenticationService.new
    result = service.verify_email(params[:email], params[:code])

    render_service_result(result)
  end

  # POST /auth/login
  def login
    service = AuthenticationService.new
    result = service.login_or_register(
      login_params,
      ip_address: client_ip,
      user_agent: user_agent
    )

    if result.success?
      action_type = User.exists?(email: login_params[:email]) ? 'logged_in' : 'registered_and_logged_in'
      log_user_activity(action_type, result.data[:user])
      render_success(
        data: {
          user: user_response(result.data[:user]),
          token: result.data[:token],
          expires_at: result.data[:expires_at]
        },
        message: result.message
      )
    else
      render_service_result(result, error_status: :unauthorized)
    end
  end

  # DELETE /auth/logout
  def logout
    token = extract_token_from_header
    service = AuthenticationService.new
    result = service.logout(token)

    log_user_activity('logged_out') if result.success?
    render_service_result(result)
  end

  # DELETE /auth/logout_all
  def logout_all
    service = AuthenticationService.new
    result = service.logout_all_devices(current_user)

    log_user_activity('logged_out_all_devices')
    render_service_result(result)
  end

  # POST /auth/send_password_reset
  def send_password_reset
    service = AuthenticationService.new
    result = service.send_password_reset_email(params[:email])

    # 为了安全，总是返回成功消息
    render_success(message: I18n.t('auth.errors.password_reset_message'))
  end

  # POST /auth/reset_password
  def reset_password
    service = AuthenticationService.new
    result = service.reset_password(
      params[:email],
      params[:code],
      params[:new_password]
    )

    render_service_result(result, error_status: :unprocessable_content)
  end

  # POST /auth/change_password
  def change_password
    service = AuthenticationService.new
    result = service.change_password(
      current_user,
      params[:current_password],
      params[:new_password]
    )

    if result.success?
      log_user_activity('changed_password')
    end

    render_service_result(result, error_status: :unprocessable_content)
  end

  # GET /auth/me
  def me
    render_success(
      data: {
        user: user_response(current_user),
        sessions: current_user.sessions.active.order(created_at: :desc).map do |session|
          {
            id: session.id,
            ip_address: session.ip_address,
            user_agent: session.user_agent,
            created_at: session.created_at,
            expires_at: session.expires_at,
            current: session.token == extract_token_from_header
          }
        end
      }
    )
  end

  # GET /auth/refresh
  def refresh
    token = extract_token_from_header
    session = Session.find_by_token(token)

    if session&.active?
      session.extend_session!
      render_success(
        data: { expires_at: session.expires_at },
        message: I18n.t('auth.success.session_refreshed')
      )
    else
      render_unauthorized(message: I18n.t('auth.errors.session_invalid'))
    end
  end

  # DELETE /auth/delete_account
  def delete_account
    service = AccountDeletionService.new
    result = service.delete_account(current_user)

    if result.success?
      log_user_activity('account_deleted', current_user)
      render_success(
        message: result.message
      )
    else
      render_service_result(result, error_status: :unprocessable_content)
    end
  end

  private

  def registration_params
    params.require(:user).permit(:email, :password, :name, :weight, :height, :birthday, :gender, 
                                 :exercise_frequency, fitness_goals: [], obstacles: [])
  end

  def login_params
    user_params = params.dig(:user) || {}
    login_data = {
      email: user_params[:email],
      password: user_params[:password],
      verification_code: params[:verification_code]
    }
    
    # 如果是验证码登录且没有用户，可能是注册，包含其他字段
    if login_data[:verification_code].present? && login_data[:password].blank?
      login_data.merge!(
        name: user_params[:name],
        weight: user_params[:weight],
        height: user_params[:height],
        birthday: user_params[:birthday],
        gender: user_params[:gender],
        exercise_frequency: user_params[:exercise_frequency],
        fitness_goals: user_params[:fitness_goals],
        obstacles: user_params[:obstacles]
      )
    end
    
    login_data.compact
  end

  def user_response(user)
    {
      id: user.id,
      email: user.email,
      name: user.name,
      display_name: user.display_name,
      roles: user.roles,
      status: user.status,
      email_verified: user.email_verified?,
      last_login_at: user.last_login_at,
      weight: user.weight,
      height: user.height,
      birthday: user.birthday,
      gender: user.gender,
      gender_text: user.gender_text,
      age: user.age,
      bmi: user.bmi,
      bmi_status: user.bmi_status,
      exercise_frequency: user.exercise_frequency,
      exercise_frequency_text: user.exercise_frequency_text,
      fitness_goals: user.fitness_goals_array,
      obstacles: user.obstacles_array,
      questionnaire_completed: user.questionnaire_completed?,
      created_at: user.created_at
    }
  end
end