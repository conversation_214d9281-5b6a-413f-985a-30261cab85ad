class UsersController < ApplicationController
  skip_before_action :authenticate_user!, only: [:questionnaire_options]
  before_action :require_admin!, except: [:show, :update, :update_profile, :questionnaire_options]
  before_action :set_user, only: [:show, :update, :update_profile, :destroy]
  before_action :authorize_user_access!, only: [:show, :update, :update_profile]

  # GET /users
  def index
    service = UserManagementService.new
    result = service.list_users(filter_params)
    
    if result.success?
      render_success(
        data: {
          users: result.data[:users].map { |user| user_response(user) }
        },
        meta: result.meta
      )
    else
      render_service_result(result)
    end
  end

  # GET /users/:id
  def show
    service = UserManagementService.new
    result = service.get_user(@user.id)
    
    if result.success?
      render_success(
        data: {
          user: user_response(result.data[:user]),
          sessions: result.data[:sessions].map { |session| session_response(session) }
        }
      )
    else
      render_service_result(result, error_status: :not_found)
    end
  end

  # POST /users
  def create
    service = UserManagementService.new
    result = service.create_user(user_params.merge(current_user: current_user))
    
    if result.success?
      log_user_activity("created_user_#{result.data[:user].id}")
      render_success(
        data: { user: user_response(result.data[:user]) },
        message: result.message,
        status: :created
      )
    else
      render_service_result(result, error_status: :unprocessable_content)
    end
  end

  # PATCH/PUT /users/:id
  def update
    # 如果是普通用户更新自己的信息，使用不同的参数
    update_params = current_user.admin? ? admin_user_params : regular_user_params
    
    if @user.update(update_params)
      log_user_activity("updated_user_#{@user.id}")
      render_success(
        data: { user: user_response(@user) },
        message: I18n.t('users.success.updated')
      )
    else
      render_validation_error(
        @user.errors,
        message: I18n.t('users.errors.update_failed')
      )
    end
  end

  # PATCH /users/:id/profile
  def update_profile
    # 更新用户个人资料（包含问卷相关信息）
    profile_params = params.require(:user).permit(
      :name, :weight, :height, :birthday, :gender, :exercise_frequency,
      fitness_goals: [], obstacles: []
    )
    
    if @user.update(profile_params)
      log_user_activity("updated_profile_user_#{@user.id}")
      render_success(
        data: { user: user_response(@user) },
        message: I18n.t('profile.success.updated')
      )
    else
      render_validation_error(
        @user.errors,
        message: I18n.t('profile.errors.update_failed')
      )
    end
  end

  # PATCH /users/me/profile
  def update_current_user_profile
    # 当前用户更新个人资料（包含问卷相关信息）
    profile_params = params.require(:user).permit(
      :name, :weight, :height, :birthday, :gender, :exercise_frequency,
      fitness_goals: [], obstacles: []
    )
    
    if current_user.update(profile_params)
      log_user_activity("updated_profile_user_#{current_user.id}")
      render_success(
        data: { user: user_response(current_user) },
        message: I18n.t('profile.success.updated')
      )
    else
      render_validation_error(
        current_user.errors,
        message: I18n.t('profile.errors.update_failed')
      )
    end
  end

  # DELETE /users/:id
  def destroy
    service = UserManagementService.new
    result = service.delete_user(@user.id)
    
    if result.success?
      log_user_activity("deleted_user_#{@user.id}")
      render_success(message: result.message)
    else
      render_service_result(result)
    end
  end

  # PATCH /users/:id/role
  def update_role
    service = UserManagementService.new
    result = service.update_user_roles(params[:id], params[:roles])
    
    if result.success?
      log_user_activity("updated_roles_user_#{params[:id]}_to_#{params[:roles]}")
      render_success(
        data: { user: user_response(result.data[:user]) },
        message: result.message
      )
    else
      render_service_result(result)
    end
  end

  # POST /users/:id/roles/:role
  def add_role
    service = UserManagementService.new
    result = service.add_user_role(params[:id], params[:role])
    
    if result.success?
      log_user_activity("added_role_#{params[:role]}_to_user_#{params[:id]}")
      render_success(
        data: { user: user_response(result.data[:user]) },
        message: result.message
      )
    else
      render_service_result(result)
    end
  end

  # DELETE /users/:id/roles/:role
  def remove_role
    service = UserManagementService.new
    result = service.remove_user_role(params[:id], params[:role])
    
    if result.success?
      log_user_activity("removed_role_#{params[:role]}_from_user_#{params[:id]}")
      render_success(
        data: { user: user_response(result.data[:user]) },
        message: result.message
      )
    else
      render_service_result(result)
    end
  end

  # PATCH /users/:id/status
  def update_status
    service = UserManagementService.new
    result = service.update_user_status(params[:id], params[:status])
    
    if result.success?
      log_user_activity("updated_status_user_#{params[:id]}_to_#{params[:status]}")
      render_success(
        data: { user: user_response(result.data[:user]) },
        message: result.message
      )
    else
      render_service_result(result)
    end
  end

  # POST /users/:id/reset_password
  def reset_password
    service = UserManagementService.new
    result = service.reset_user_password(params[:id], params[:new_password])
    
    if result.success?
      log_user_activity("reset_password_user_#{params[:id]}")
      render_success(message: result.message)
    else
      render_service_result(result, error_status: :unprocessable_content)
    end
  end

  # POST /users/:id/verify_email
  def verify_email
    service = UserManagementService.new
    result = service.verify_user_email(params[:id])
    
    if result.success?
      log_user_activity("verified_email_user_#{params[:id]}")
      render_success(message: result.message)
    else
      render_service_result(result)
    end
  end

  # DELETE /users/:id/sessions
  def revoke_sessions
    service = UserManagementService.new
    result = service.revoke_user_sessions(params[:id])
    
    if result.success?
      log_user_activity("revoked_sessions_user_#{params[:id]}")
      render_success(message: result.message)
    else
      render_service_result(result)
    end
  end

  # GET /users/statistics
  def statistics
    service = UserManagementService.new
    result = service.get_user_statistics
    
    render_success(data: result.data)
  end

  # GET /users/questionnaire_options
  def questionnaire_options
    render_success(
      data: {
        exercise_frequencies: exercise_frequency_options,
        fitness_goals: fitness_goals_options,
        obstacles: obstacles_options
      }
    )
  end

  private

  def set_user
    @user = User.find(params[:id])
  end

  def authorize_user_access!
    authorize_user!(@user)
  end

  def filter_params
    params.permit(:search, :role, :status, :email_verified, :gender, :sort_by, :page, :per_page)
  end

  def user_params
    permitted = [:name, :email, :weight, :height, :birthday, :gender, :exercise_frequency]
    
    # 管理员可以设置更多字段
    if current_user.admin?
      permitted += [:password, :roles, :status, :skip_email_verification]
    end
    
    params.require(:user).permit(*permitted, roles: [], fitness_goals: [], obstacles: [])
  end

  def admin_user_params
    params.require(:user).permit(
      :name, :email, :weight, :height, :birthday, :gender, :exercise_frequency,
      :password, :status, roles: [], fitness_goals: [], obstacles: []
    )
  end

  def regular_user_params
    params.require(:user).permit(
      :name, :weight, :height, :birthday, :gender, :exercise_frequency,
      fitness_goals: [], obstacles: []
    )
  end

  def user_response(user)
    {
      id: user.id,
      email: user.email,
      name: user.name,
      display_name: user.display_name,
      roles: user.roles,
      primary_role: user.primary_role,
      roles_text: user.roles_text,
      status: user.status,
      email_verified: user.email_verified?,
      last_login_at: user.last_login_at,
      last_login_ip: user.last_login_ip,
      weight: user.weight,
      height: user.height,
      birthday: user.birthday,
      gender: user.gender,
      gender_text: user.gender_text,
      age: user.age,
      bmi: user.bmi,
      bmi_status: user.bmi_status,
      exercise_frequency: user.exercise_frequency,
      exercise_frequency_text: user.exercise_frequency_text,
      fitness_goals: user.fitness_goals_array,
      obstacles: user.obstacles_array,
      questionnaire_completed: user.questionnaire_completed?,
      created_at: user.created_at,
      updated_at: user.updated_at
    }
  end

  def session_response(session)
    {
      id: session.id,
      ip_address: session.ip_address,
      user_agent: session.user_agent,
      created_at: session.created_at,
      expires_at: session.expires_at,
      active: session.active?
    }
  end

  def exercise_frequency_options
    [
      { value: 'occasionally', label: I18n.t('user.exercise_frequency.occasionally'), description: '0-2 times per week' },
      { value: 'regular', label: I18n.t('user.exercise_frequency.regular'), description: '3-5 times per week' },
      { value: 'athlete', label: I18n.t('user.exercise_frequency.athlete'), description: '6+ times per week' }
    ]
  end
  
  def fitness_goals_options
    [
      { value: 'weight_loss', label: I18n.t('user.fitness_goals.weight_loss') },
      { value: 'muscle_gain', label: I18n.t('user.fitness_goals.muscle_gain') },
      { value: 'endurance', label: I18n.t('user.fitness_goals.endurance') },
      { value: 'strength', label: I18n.t('user.fitness_goals.strength') },
      { value: 'flexibility', label: I18n.t('user.fitness_goals.flexibility') },
      { value: 'health', label: I18n.t('user.fitness_goals.health') },
      { value: 'stress_relief', label: I18n.t('user.fitness_goals.stress_relief') },
      { value: 'competition', label: I18n.t('user.fitness_goals.competition') }
    ]
  end
  
  def obstacles_options
    [
      { value: 'lack_consistency', label: I18n.t('user.obstacles.lack_consistency') },
      { value: 'unhealthy_work_habits', label: I18n.t('user.obstacles.unhealthy_work_habits') },
      { value: 'lack_support', label: I18n.t('user.obstacles.lack_support') },
      { value: 'busy_schedule', label: I18n.t('user.obstacles.busy_schedule') },
      { value: 'lack_motivation', label: I18n.t('user.obstacles.lack_motivation') }
    ]
  end
end