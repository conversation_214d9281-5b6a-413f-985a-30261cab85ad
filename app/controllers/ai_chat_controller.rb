# AI聊天控制器
# 处理AI聊天相关的REST API请求
class AiChatController < ApplicationController
  before_action :set_chat_room, only: [:show, :send_message]
  before_action :check_room_access, only: [:show, :send_message]

  # GET /ai_chat/rooms
  # 获取用户的聊天室列表
  def rooms
    @rooms = current_user.participating_chat_rooms
                        .includes(:last_message, :ai_chat_sessions)
                        .active
                        .order(updated_at: :desc)
    
    render_success(
      data: {
        rooms: @rooms.map do |room|
          {
            id: room.id,
            name: room.name,
            description: room.description,
            last_message: room.last_message&.slice(:id, :content, :message_type, :created_at),
            total_tokens: room.total_token_usage,
            participant_count: room.chat_room_participants.active.count,
            created_at: room.created_at,
            updated_at: room.updated_at
          }
        end
      }
    )
  end

  # POST /ai_chat/rooms
  # 创建新的聊天室
  def create_room
    @room = current_user.chat_rooms.build(room_params)
    
    if @room.save
      # 创建AI聊天会话
      @session = @room.ai_chat_sessions.create!(
        user: current_user,
        session_name: @room.name,
        model_config: default_model_config
      )
      
      render_success(
        data: {
          room: @room.slice(:id, :name, :description, :status),
          session: @session.slice(:id, :session_name, :model_config)
        },
        message: I18n.t('ai_chat.success.session_created'),
        status: :created
      )
    else
      render_validation_error(@room.errors, I18n.t('ai_chat.errors.session_creation_failed'))
    end
  end

  # GET /ai_chat/rooms/:id
  # 获取聊天室详情和消息历史
  def show
    @messages = @room.chat_messages
                    .includes(:user, :parent_message)
                    .order(:created_at)
                    .limit(100) # 最近100条消息
    
    render_success(
      data: {
        room: @room.slice(:id, :name, :description, :status),
        messages: @messages.map do |msg|
          {
            id: msg.id,
            content: msg.content,
            message_type: msg.message_type,
            user: msg.user&.slice(:id, :name, :email),
            token_count: msg.token_count,
            model_name: msg.ai_model_name,
            parent_message_id: msg.parent_message_id,
            status: msg.status,
            created_at: msg.created_at
          }
        end,
        total_tokens: @room.total_token_usage
      }
    )
  end

  # POST /ai_chat/rooms/:id/send_message
  # 发送消息给AI（REST API）
  def send_message
    message_content = params[:message]&.strip
    
    if message_content.blank?
      return render_error(
        message: I18n.t('ai_chat.errors.message_empty'),
        status: :bad_request
      )
    end

    # 创建用户消息
    user_message = @room.chat_messages.create!(
      user: current_user,
      content: message_content,
      message_type: :user,
      status: :sent
    )

    # 异步处理AI响应
    AiChatProcessorJob.perform_later(
      chat_room_id: @room.id,
      user_message_id: user_message.id,
      user_id: current_user.id
    )

    # 立即返回用户消息
    render_success(
      data: {
        user_message: {
          id: user_message.id,
          content: user_message.content,
          message_type: user_message.message_type,
          created_at: user_message.created_at
        }
      },
      message: '消息发送成功，AI正在处理中',
      status: :created
    )
  end

  # GET /ai_chat/sessions
  # 获取用户的AI聊天会话列表
  def sessions
    @sessions = current_user.ai_chat_sessions
                           .includes(:chat_room)
                           .active
                           .recent
                           .limit(50)
    
    render_success(
      data: {
        sessions: @sessions.map do |session|
          {
            id: session.id,
            session_name: session.session_name,
            chat_room: session.chat_room.slice(:id, :name),
            total_tokens: session.total_tokens,
            total_prompt_tokens: session.total_prompt_tokens,
            total_completion_tokens: session.total_completion_tokens,
            last_message_at: session.last_message_at,
            created_at: session.created_at,
            cost_estimate: session.cost_estimate
          }
        end
      }
    )
  end

  # GET /ai_chat/statistics
  # 获取用户的AI聊天统计信息
  def statistics
    stats = {
      total_rooms: current_user.participating_chat_rooms.active.count,
      total_sessions: current_user.ai_chat_sessions.active.count,
      total_messages: current_user.chat_messages.count,
      total_tokens: current_user.ai_chat_sessions.sum(:total_tokens),
      total_prompt_tokens: current_user.ai_chat_sessions.sum(:total_prompt_tokens),
      total_completion_tokens: current_user.ai_chat_sessions.sum(:total_completion_tokens),
      last_chat_at: current_user.ai_chat_sessions.maximum(:last_message_at)
    }

    render_success(
      data: { statistics: stats }
    )
  end

  # GET /ai_chat/models
  # 获取可用的AI模型列表
  def models
    result = AiChatService.available_models
    render_service_result(result)
  end

  private

  def set_chat_room
    @room = ChatRoom.find(params[:id])
  rescue ActiveRecord::RecordNotFound
    render_not_found(I18n.t('ai_chat.errors.session_not_found'))
  end

  def check_room_access
    unless @room.participant?(current_user)
      render_forbidden(message: I18n.t('ai_chat.errors.access_denied'))
    end
  end

  def room_params
    params.require(:room).permit(:name, :description)
  end

  def default_model_config
    Rails.application.config.moonshot[:model_config].merge(
      model: Rails.application.config.moonshot[:default_model]
    )
  end
end