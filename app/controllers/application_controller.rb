class ApplicationController < ActionController::API
  before_action :set_locale

  include Pagy::Backend
  include Authentication
  include ControllerResponse

  # 测试方法（应该在生产环境中删除）
  def xxx
    bucket = Aws::S3::Bucket.new("xiancheng")
    bucket.objects.each do |object|
      puts object.key
    end

    client = OpenAI::Client.new
    client.chat(
      parameters: {
        model: "moonshot-v1-128k-vision-preview", # Required.
        messages: [{ role: "user", content: "What can you do?"}], # Required.
        temperature: 0.7,
        stream: proc do |chunk, event|
          puts "Received chunk:"
          puts chunk.inspect
          puts "Content:"
          print chunk.dig("choices", 0, "delta", "content")
          puts "\n\n"
          puts event
        end
      }
    )
  end

  private

  # 统一处理语言设置
  def set_locale
    # 优先级：URL参数 > Accept-Language头 > 默认语言
    locale = params[:locale] ||
             extract_locale_from_accept_language ||
             I18n.default_locale

    # 确保语言在可用列表中
    if I18n.available_locales.include?(locale.to_sym)
      I18n.locale = locale
    else
      I18n.locale = I18n.default_locale
    end
  end

  # 从 Accept-Language 头中提取语言
  def extract_locale_from_accept_language
    return nil unless request.env["HTTP_ACCEPT_LANGUAGE"]

    # 解析 Accept-Language 头，支持 zh, zh-CN, en 等格式
    accepted_languages = request.env["HTTP_ACCEPT_LANGUAGE"].split(",").map do |lang|
      lang.strip.split(";").first.strip
    end

    # 查找匹配的语言
    accepted_languages.each do |lang|
      # 完全匹配
      return lang if I18n.available_locales.include?(lang.to_sym)

      # 语言代码匹配（如 zh 匹配 zh-CN）
      if lang == "zh"
        return "zh-CN" if I18n.available_locales.include?(:'zh-CN')
      elsif lang == "en"
        return "en" if I18n.available_locales.include?(:en)
      end
    end

    nil
  end
end
