class VerificationCodesController < ApplicationController
  skip_before_action :authenticate_user!, only: [:send_code, :verify]
  before_action :validate_email_param, only: [:send_code]
  before_action :validate_verify_params, only: [:verify]

  # POST /verification_codes/send
  # Send verification code
  def send_code
    email = params[:email]
    purpose = params[:purpose] || 'email_verification'
    locale = params[:locale] || I18n.default_locale

    result = VerificationCodeService.send_code(email, purpose, locale)
    
    if result.success?
      render_success(
        data: result.data,
        message: result.message
      )
    else
      case result.message
      when /too_frequent|频率/
        render_error(
          message: result.message,
          status: :too_many_requests
        )
      when /send_failed|发送失败/
        render_error(
          message: result.message,
          status: :internal_server_error
        )
      else
        render_service_result(result)
      end
    end
  end

  # POST /verification_codes/verify  
  # Verify verification code
  def verify
    email = params[:email]
    code = params[:code]
    purpose = params[:purpose] || 'email_verification'
    locale = params[:locale] || I18n.default_locale

    result = VerificationCodeService.verify_code(email, code, purpose, locale)
    
    if result.success?
      render_success(message: result.message)
    else
      render_service_result(result, error_status: :unprocessable_content)
    end
  end

  private

  def validate_email_param
    email = params[:email]
    locale = params[:locale] || I18n.default_locale
    
    I18n.with_locale(locale) do
      if email.blank?
        render_error(
          message: I18n.t('verification_code.errors.email_required'),
          status: :bad_request
        )
        return
      end

      unless email.match?(URI::MailTo::EMAIL_REGEXP)
        render_error(
          message: I18n.t('verification_code.errors.email_format'),
          status: :bad_request
        )
        return
      end
    end
  end

  def validate_verify_params
    locale = params[:locale] || I18n.default_locale
    
    I18n.with_locale(locale) do
      if params[:email].blank? || params[:code].blank?
        render_error(
          message: I18n.t('verification_code.errors.params_required'),
          status: :bad_request
        )
        return
      end
    end
  end
end
