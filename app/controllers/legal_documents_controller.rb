class LegalDocumentsController < ApplicationController
  # 跳过认证检查，法律文档应该是公开访问的
  skip_before_action :authenticate_user!
  
  # 获取隐私政策
  # GET /legal_documents/privacy_policy
  def privacy_policy
    render_success(
      data: {
        title: I18n.t('legal_documents.privacy_policy.title'),
        content: I18n.t('legal_documents.privacy_policy.content'),
        last_updated: I18n.t('legal_documents.privacy_policy.last_updated'),
        version: I18n.t('legal_documents.privacy_policy.version')
      }
    )
  end

  # 获取服务条款
  # GET /legal_documents/terms_of_service
  def terms_of_service
    render_success(
      data: {
        title: I18n.t('legal_documents.terms_of_service.title'),
        content: I18n.t('legal_documents.terms_of_service.content'),
        last_updated: I18n.t('legal_documents.terms_of_service.last_updated'),
        version: I18n.t('legal_documents.terms_of_service.version')
      }
    )
  end
end