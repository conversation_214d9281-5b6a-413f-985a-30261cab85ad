module Authentication
  extend ActiveSupport::Concern

  included do
    before_action :authenticate_user!
  end

  private

  # 从请求头中获取当前用户
  def current_user
    return @current_user if defined?(@current_user)
    token = extract_token_from_header
    @current_user = AuthenticationService.authenticate_by_token(token)
  end

  # 检查用户是否已登录
  def user_signed_in?
    current_user.present?
  end

  # 要求用户登录
  def authenticate_user!
    unless user_signed_in?
      render json: { error: I18n.t('authentication.errors.login_required') }, status: :unauthorized
    end
  end

  # 要求管理员权限
  def require_admin!
    unless current_user&.admin?
      render json: { error: I18n.t('authentication.errors.admin_required') }, status: :forbidden
    end
  end

  # 检查用户是否有指定角色
  def require_role!(role)
    unless current_user&.has_role?(role)
      render json: { error: I18n.t('authentication.errors.role_required', role: role) }, status: :forbidden
    end
  end

  # 检查用户是否有任一指定角色
  def require_any_role!(*roles)
    unless current_user && roles.any? { |role| current_user.has_role?(role) }
      render json: { error: I18n.t('authentication.errors.any_role_required', roles: roles.join(', ')) }, status: :forbidden
    end
  end

  # 检查用户是否有权限访问资源
  def authorize_user!(resource_user)
    unless current_user&.admin? || current_user == resource_user
      render json: { error: I18n.t('authentication.errors.permission_denied') }, status: :forbidden
    end
  end

  # 从请求头中提取令牌
  def extract_token_from_header
    auth_header = request.headers['Authorization']
    return nil unless auth_header
    
    # 支持 "Bearer token" 格式
    if auth_header.start_with?('Bearer ')
      auth_header.split(' ').last
    else
      auth_header
    end
  end

  # 获取客户端 IP 地址
  def client_ip
    request.remote_ip
  end

  # 获取用户代理
  def user_agent
    request.user_agent
  end

  # 记录用户活动
  def log_user_activity(action, user = current_user)
    return unless user
    
    Rails.logger.info "User Activity: #{user.email} performed #{action} from #{client_ip}"
  end
end