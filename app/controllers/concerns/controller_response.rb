# Controller响应助手模块
module ControllerResponse
  extend ActiveSupport::Concern
  include ApiResponse

  included do
    # 全局异常处理
    rescue_from StandardError, with: :handle_standard_error
    rescue_from ActiveRecord::RecordNotFound, with: :handle_not_found
    rescue_from ActiveRecord::RecordInvalid, with: :handle_record_invalid
    rescue_from ActionController::ParameterMissing, with: :handle_parameter_missing
    rescue_from ArgumentError, with: :handle_argument_error
  end

  # 渲染ServiceResult
  def render_service_result(result, success_status: :ok, error_status: nil)
    if result.success?
      render json: result.to_api_response(status: success_status), status: success_status
    else
      # 根据错误类型确定状态码
      status = error_status || determine_error_status(result)
      render json: result.to_api_response(status: status), status: status
    end
  end

  # 渲染成功响应
  def render_success(data: nil, message: nil, meta: nil, status: :ok)
    render json: success_response(data: data, message: message, meta: meta, status: status), 
           status: status
  end

  # 渲染错误响应
  def render_error(message:, details: nil, status: :bad_request)
    render json: error_response(message: message, details: details, status: status), 
           status: status
  end

  # 渲染分页响应
  def render_paginated(data:, pagination:, message: nil, status: :ok)
    render json: paginated_response(data: data, pagination: pagination, message: message, status: status), 
           status: status
  end

  # 渲染验证错误
  def render_validation_error(errors, message: nil)
    response = validation_error_response(errors, message: message)
    render json: response, status: :unprocessable_content
  end

  # 渲染未找到错误
  def render_not_found(resource_name = nil)
    response = not_found_response(resource_name)
    render json: response, status: :not_found
  end

  # 渲染未授权错误
  def render_unauthorized(message: nil)
    response = unauthorized_response(message: message)
    render json: response, status: :unauthorized
  end

  # 渲染禁止访问错误
  def render_forbidden(message: nil)
    response = forbidden_response(message: message)
    render json: response, status: :forbidden
  end

  private

  # 根据ServiceResult确定错误状态码
  def determine_error_status(result)
    message = result.message.to_s.downcase
    
    case message
    when /not found|不存在|未找到/
      :not_found
    when /unauthorized|未授权|未认证/
      :unauthorized
    when /forbidden|禁止|权限不足/
      :forbidden
    when /validation|验证失败|参数错误/
      :unprocessable_content
    when /conflict|冲突|已存在/
      :conflict
    when /too many|频率|限制/
      :too_many_requests
    else
      :bad_request
    end
  end

  # 异常处理方法
  def handle_standard_error(exception)
    Rails.logger.error "Unhandled error: #{exception.class.name} - #{exception.message}"
    Rails.logger.error exception.backtrace.join("\n")
    
    render_error(
      message: I18n.t('errors.internal_server_error'),
      status: :internal_server_error
    )
  end

  def handle_not_found(exception)
    Rails.logger.info "Record not found: #{exception.message}"
    render_not_found
  end

  def handle_record_invalid(exception)
    Rails.logger.info "Record invalid: #{exception.record.errors.full_messages}"
    render_validation_error(exception.record.errors)
  end

  def handle_parameter_missing(exception)
    Rails.logger.info "Parameter missing: #{exception.param}"
    render_error(
      message: I18n.t('errors.parameter_missing', param: exception.param),
      status: :bad_request
    )
  end

  def handle_argument_error(exception)
    # 打印错误栈
    Rails.logger.error exception.backtrace.join("\n")
    Rails.logger.info "Argument error: #{exception.message}"
    render_error(
      message: I18n.t('errors.invalid_argument'),
      details: [exception.message],
      status: :bad_request
    )
  end
end