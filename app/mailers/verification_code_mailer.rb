class VerificationCodeMailer < ApplicationMailer
  default from: Settings.mailer_from

  def send_verification_code(email, code, purpose = 'email_verification', locale = I18n.default_locale)
    @email = email
    @code = code
    @purpose = purpose
    @locale = locale.to_s
    
    I18n.with_locale(@locale) do
      @purpose_text = I18n.t("verification_code.purpose.#{purpose}")
      @company_name = I18n.t('company.name')
      
      mail(
        to: email,
        subject: I18n.t('verification_code.mailer.subject', 
                       purpose: @purpose_text, 
                       code: @code)
      )
    end
  end

  private

  def purpose_text(purpose)
    I18n.t("verification_code.purpose.#{purpose}", default: I18n.t('verification_code.purpose.default'))
  end
end
