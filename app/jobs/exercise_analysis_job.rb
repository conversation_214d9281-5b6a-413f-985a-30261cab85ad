# 运动分析作业 - 异步处理用户上传的运动数据图片
class ExerciseAnalysisJob < ApplicationJob
  queue_as :default

  # 重试配置
  retry_on StandardError, wait: 3.seconds, attempts: 3

  def perform(exercise_analysis_id)
    @exercise_analysis = ExerciseAnalysis.find(exercise_analysis_id)
    @user = @exercise_analysis.user

    Rails.logger.info "Processing exercise analysis for user #{@user.id}, analysis #{exercise_analysis_id}"

    begin
      # 更新状态为分析中
      @exercise_analysis.update!(status: :analyzing)

      # 获取所有图片附件
      images = @exercise_analysis.images
      Rails.logger.info "Found #{images.count} images to analyze"

      # 如果没有图片，标记为失败
      if images.empty?
        @exercise_analysis.mark_as_failed("No images provided for analysis")
        return
      end

      # 调用AI服务进行分析
      messages = generate_analysis_prompt(images)
      Rails.logger.info "Generated analysis prompt with #{messages.length} messages"

      # 调用AI生成分析报告
      # 创建一个临时的AI会话用于分析
      ai_service = AiChatService.new(nil, nil) # 不需要聊天室和会话上下文
      ai_response = ai_service.generate_text_response(messages)

      if ai_response.failure?
        raise StandardError, ai_response.message
      end

      # 解析AI响应
      analysis_result = parse_ai_response(ai_response.data[:content])

      # 更新分析结果
      @exercise_analysis.update_analysis_result(analysis_result)

      Rails.logger.info "Exercise analysis completed successfully for user #{@user.id}"

    rescue => e
      Rails.logger.error "Exercise analysis failed: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")

      # 标记分析为失败状态
      @exercise_analysis.mark_as_failed(e.message)

      # 重新抛出异常以触发重试机制
      raise e
    end
  end

  private

  # 生成分析提示词 - 返回一个包含图片和文本的内容列表
  def generate_analysis_prompt(images)
    # 读取提示词模板
    prompt_template = File.read(Rails.root.join('uni-starter/prompts/analysis_report_prompt.md'))

    # 将用户信息填充到提示词模板中
    user_info = {
      name: @user.name || '用户',
      age: @user.age || '未知',
      gender: @user.gender_text || '未知',
      height: @user.height || '未知',
      weight: @user.weight || '未知',
      bmi: @user.bmi || '未知',
      bmi_status: @user.bmi_status || '未知',
      exercise_frequency: @user.exercise_frequency_text || '未知',
      fitness_goals: (@user.fitness_goals_array || []).join(', ') || '未知'
    }

    # 替换模板中的用户信息占位符
    text_prompt = prompt_template.dup
    user_info.each do |key, value|
      text_prompt.gsub!("{{#{key}}}", value.to_s)
    end

    # 移除图片占位符，因为我们将单独处理图片
    text_prompt.gsub!("{{images}}", '')

    # 创建一个包含图片和文本的内容列表
    content_list = []
    
    # 直接处理Active Storage图片附件，转换为base64格式
    images.each do |image|
      begin
        # 直接从Active Storage读取图片内容，转换为base64编码
        base64_image = image_to_base64(image)
        
        content_list << {
          type: 'image_url',
          image_url: {
            url: base64_image
          }
        }
      rescue => e
        Rails.logger.error "Failed to process image: #{e.message}"
        # 如果处理失败，可以跳过该图片或使用其他处理方式
      end
    end
    
    # 如果没有成功添加任何图片，标记为失败
    if content_list.empty?
      raise StandardError, "Failed to process any images for analysis"
    end
    
    # 最后添加文本提示
    content_list << {
      type: 'text',
      text: text_prompt.strip
    }
    
    # 返回messages列表，包含system和user消息
    [
      {
        role: 'system',
        content: '你是 Kimi。'
      },
      {
        role: 'user',
        content: content_list
      }
    ]
  end

  # 将Active Storage图片转换为base64编码
  def image_to_base64(image)
    require 'base64'
    
    # 直接从Active Storage读取文件内容
    image_content = image.download
    
    # 获取图片MIME类型
    content_type = image.content_type || 'image/jpeg' # 默认使用jpeg类型
    
    # 生成base64编码的data URL
    "data:#{content_type};base64,#{Base64.strict_encode64(image_content)}"
  rescue => e
    Rails.logger.error "Error converting image to base64: #{e.message}"
    raise StandardError, "Failed to process image: #{e.message}"
  end
  
  # 解析AI响应
  def parse_ai_response(content)
    # 这里可以根据实际的AI响应格式进行解析
    # 假设AI返回的是结构化的JSON数据
    # 如果不是JSON格式，可能需要使用正则表达式或其他方法解析
    begin
      # 如果是 ```json ...``` 包裹的内容，提取出来
      json_match = content.match(/```json\s*([\s\S]*?)\s*```/)
      content = json_match[1] if json_match
      # 尝试直接解析JSON
      JSON.parse(content)
    rescue JSON::ParserError
      # 如果不是JSON，尝试提取结构化信息
      # 这里是一个简化的示例，实际解析逻辑可能更复杂
      result = {
        overall_performance: extract_section(content, '整体表现'),
        pace_and_heart_rate_analysis: extract_section(content, '配速与心率分析'),
        heart_rate_zone_distribution: extract_section(content, '心率区间分布'),
        calorie_consumption_analysis: extract_section(content, '热量消耗与能量代谢分析'),
        cadence_and_running_efficiency: extract_section(content, '步频与跑步效率'),
        environmental_factor_impact: extract_section(content, '环境系数影响'),
        exercise_effect_and_physical_improvement: extract_section(content, '运动效果与身体机能提升'),
        training_quality_evaluation_and_suggestions: extract_section(content, '训练质量评价与建议'),
        weekly_plan_arrangement: extract_section(content, '周计划安排'),
        nutrition_suggestions: extract_section(content, '营养建议'),
        planned_vs_actual_execution: extract_section(content, '原计划VS实际执行'),
        exercise_performance_analysis: extract_section(content, '运动表现分析'),
        raw_content: content # 保存原始内容
      }
      result
    end
  end

  # 从文本中提取特定部分的内容
  def extract_section(content, section_title)
    # 这里是一个简化的提取逻辑，实际应用中可能需要更复杂的正则表达式
    section_match = content.match(/^#{section_title}\n---*\n([\s\S]*?)(?=\n[#=]|\Z)/)
    section_match ? section_match[1].strip : ""
  end
end