class CleanupExpiredVerificationCodesJob < ApplicationJob
  queue_as :low

  def perform
    # 删除过期超过1天的验证码
    deleted_count = VerificationCode.where('expires_at < ?', 1.day.ago).delete_all
    Rails.logger.info "清理过期验证码完成，共删除 #{deleted_count} 条记录"
    
    # 删除已使用超过1周的验证码
    used_deleted_count = VerificationCode.where('used_at IS NOT NULL AND used_at < ?', 1.week.ago).delete_all
    Rails.logger.info "清理已使用验证码完成，共删除 #{used_deleted_count} 条记录"
    
    { deleted_expired: deleted_count, deleted_used: used_deleted_count }
  end
end