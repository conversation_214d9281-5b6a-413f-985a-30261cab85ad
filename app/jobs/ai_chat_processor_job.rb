# AI聊天处理作业 - 异步处理AI响应
class AiChatProcessorJob < ApplicationJob
  queue_as :default

  # 重试配置
  retry_on StandardError, wait: 3.seconds, attempts: 3

  def perform(chat_room_id:, user_message_id:, user_id:)
    @chat_room = ChatRoom.find(chat_room_id)
    @user_message = ChatMessage.find(user_message_id)
    @user = User.find(user_id)
    
    # 先创建AI会话
    @ai_session = find_or_create_ai_session

    Rails.logger.info "Processing AI chat for room #{chat_room_id}, message #{user_message_id}"

    # 创建AI响应消息（初始状态为processing）
    ai_message = create_ai_message_placeholder

    begin
      # 调用AI服务获取流式响应
      ai_response = AiChatService.new(@chat_room, @ai_session).generate_response(@user_message) do |chunk, accumulated_content|
        # 实时更新AI消息内容并通过WebSocket广播
        Rails.logger.info "AI Job处理流式响应 - 内容块: '#{chunk}' (长度: #{chunk.length})"
        Rails.logger.info "AI Job处理流式响应 - 累积内容长度: #{accumulated_content.length}, 前100字符: '#{accumulated_content[0..100]}'"
        
        # 只有当累积内容不为空时才更新消息，避免验证失败
        if accumulated_content.present? && accumulated_content.strip.present?
          begin
            Rails.logger.info "更新AI消息到数据库，消息ID: #{ai_message.id}, 内容长度: #{accumulated_content.length}"
            ai_message.update!(content: accumulated_content)
            broadcast_streaming_chunk(ai_message, chunk, accumulated_content)
          rescue ActiveRecord::RecordInvalid => e
            Rails.logger.error "Failed to update AI message during streaming: #{e.message}"
            # 继续流式处理，但记录错误
          end
        else
          Rails.logger.warn "跳过空的累积内容更新，内容长度: #{accumulated_content.length}"
        end
      end
      if ai_response.failure?
        raise StandardError, ai_response.message
      end
      ai_response = ai_response.data
      # 更新AI消息内容和token统计
      final_content = ai_response[:content].present? ? ai_response[:content] : "抱歉，AI服务返回了空内容，请重试。"
      Rails.logger.info "AI处理完成 - 最终内容长度: #{final_content.length}, 前200字符: '#{final_content[0..200]}'"
      Rails.logger.info "AI处理完成 - Token统计: total=#{ai_response[:total_tokens]}, prompt=#{ai_response[:prompt_tokens]}, completion=#{ai_response[:completion_tokens]}"
      
      ai_message.update!(
        content: final_content,
        status: :sent,
        token_count: ai_response[:total_tokens],
        prompt_tokens: ai_response[:prompt_tokens],
        completion_tokens: ai_response[:completion_tokens],
        ai_model_name: ai_response[:model_name],
        metadata: ai_response[:metadata]
      )

      # 更新AI会话的token统计
      @ai_session.update_token_usage!(
        prompt_tokens: ai_response[:prompt_tokens],
        completion_tokens: ai_response[:completion_tokens]
      )

      # 通过WebSocket广播AI响应
      broadcast_ai_response(ai_message)

      Rails.logger.info "AI response generated successfully for message #{user_message_id}"

    rescue => e
      Rails.logger.error "AI chat processing failed: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")

      # 标记AI消息为失败状态
      ai_message.update!(
        content: "抱歉，AI服务暂时不可用，请稍后重试。",
        status: :failed,
        metadata: { error: e.message }
      )

      # 广播错误消息
      broadcast_error_response(ai_message, e.message)

      # 重新抛出异常以触发重试机制
      raise e
    end
  end

  private

  def find_or_create_ai_session
    @chat_room.ai_chat_sessions.find_by(user: @user) ||
    @chat_room.ai_chat_sessions.create!(
      user: @user,
      session_name: "Chat Session #{Time.current.strftime('%Y-%m-%d %H:%M')}",
      model_config: default_model_config
    )
  end

  def create_ai_message_placeholder
    # 获取默认模型名称
    default_model = Rails.application.config.moonshot[:default_model]
    model_name = @ai_session&.model_config&.dig('model') || default_model
    
    @chat_room.chat_messages.create!(
      content: "AI正在思考中...",
      message_type: :ai,
      status: :processing,
      parent_message: @user_message,
      ai_model_name: model_name
    )
  end

  def broadcast_streaming_chunk(ai_message, chunk, accumulated_content)
    ActionCable.server.broadcast(
      "chat_room_#{@chat_room.id}",
      {
        type: 'ai_streaming',
        message: {
          id: ai_message.id,
          content: accumulated_content,
          chunk: chunk,
          message_type: ai_message.message_type,
          parent_message_id: ai_message.parent_message_id,
          status: 'streaming',
          updated_at: Time.current.iso8601
        }
      }
    )
  end

  def broadcast_ai_response(ai_message)
    Rails.logger.info """Broadcasting AI response
room_id: #{@chat_room.id}
message_id: #{ai_message.id}
content: #{ai_message.content[0..100]}... (length: #{ai_message.content.length})
token_count: #{ai_message.token_count}
prompt_tokens: #{ai_message.prompt_tokens}
completion_tokens: #{ai_message.completion_tokens}
model_name: #{ai_message.ai_model_name}
status: #{ai_message.status}
created_at: #{ai_message.created_at.iso8601}
updated_at: #{ai_message.updated_at.iso8601}
"""
    ActionCable.server.broadcast(
      "chat_room_#{@chat_room.id}",
      {
        type: 'ai_response',
        message: {
          id: ai_message.id,
          content: ai_message.content,
          message_type: ai_message.message_type,
          token_count: ai_message.token_count,
          prompt_tokens: ai_message.prompt_tokens,
          completion_tokens: ai_message.completion_tokens,
          model_name: ai_message.ai_model_name,
          parent_message_id: ai_message.parent_message_id,
          status: ai_message.status,
          created_at: ai_message.created_at.iso8601,
          updated_at: ai_message.updated_at.iso8601
        },
        session_stats: {
          total_tokens: @ai_session.total_tokens,
          total_prompt_tokens: @ai_session.total_prompt_tokens,
          total_completion_tokens: @ai_session.total_completion_tokens
        }
      }
    )
  end

  def broadcast_error_response(ai_message, error_message)
    ActionCable.server.broadcast(
      "chat_room_#{@chat_room.id}",
      {
        type: 'ai_error',
        message: {
          id: ai_message.id,
          content: ai_message.content,
          message_type: ai_message.message_type,
          status: ai_message.status,
          created_at: ai_message.created_at.iso8601,
          parent_message_id: ai_message.parent_message_id
        },
        error: {
          message: error_message,
          timestamp: Time.current.iso8601
        }
      }
    )
  end

  def default_model_config
    Rails.application.config.moonshot[:model_config].merge(
      model: Rails.application.config.moonshot[:default_model]
    )
  end
end