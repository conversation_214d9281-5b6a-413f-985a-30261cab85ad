<% I18n.with_locale(@locale) do %>
<%= I18n.t('company.name') %><% if I18n.locale == :'zh-CN' %> / <%= I18n.t('company.name_en') %><% else %> / <%= I18n.t('company.name_cn') %><% end %> - <%= @purpose_text %>

<%= I18n.t('verification_code.mailer.greeting') %>

<%= I18n.t('verification_code.mailer.instruction', purpose: @purpose_text) %>

<%= I18n.t('verification_code.mailer.code_label') %><%= @code %>

<%= I18n.t('verification_code.mailer.important_notice') %>
<% I18n.t('verification_code.mailer.notice_items').each_with_index do |item, index| %>
<%= index + 1 %>. <%= item %>
<% end %>

<%= I18n.t('verification_code.mailer.support') %>

<%= I18n.t('verification_code.mailer.footer_notice') %>
<%= I18n.t('verification_code.mailer.copyright', year: Date.current.year, company: I18n.t('company.name')) %>
<% end %>