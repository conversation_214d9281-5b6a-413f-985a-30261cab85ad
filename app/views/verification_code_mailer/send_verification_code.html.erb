<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= I18n.t('verification_code.mailer.subject', purpose: @purpose_text, code: @code) %></title>
    <style>
      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        line-height: 1.6;
        color: #333;
        max-width: 600px;
        margin: 0 auto;
        padding: 20px;
        background-color: #f5f5f5;
      }
      .container {
        background-color: white;
        padding: 40px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      }
      .header {
        text-align: center;
        margin-bottom: 30px;
      }
      .brand {
        font-size: 24px;
        font-weight: bold;
        color: #007bff;
        margin-bottom: 10px;
      }
      .brand-en {
        font-size: 14px;
        color: #666;
        font-weight: normal;
      }
      .code-box {
        background-color: #f8f9fa;
        border: 2px dashed #007bff;
        border-radius: 8px;
        padding: 20px;
        text-align: center;
        margin: 20px 0;
      }
      .code {
        font-size: 32px;
        font-weight: bold;
        color: #007bff;
        letter-spacing: 6px;
        font-family: 'Courier New', monospace;
      }
      .footer {
        margin-top: 30px;
        font-size: 14px;
        color: #666;
        text-align: center;
      }
      .warning {
        background-color: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 4px;
        padding: 15px;
        margin: 20px 0;
        color: #856404;
      }
    </style>
  </head>
  <body>
    <% I18n.with_locale(@locale) do %>
      <div class="container">
        <div class="header">
          <div class="brand">
            <%= I18n.t('company.name') %>
            <% if I18n.locale == :'zh-CN' %>
              <div class="brand-en"><%= I18n.t('company.name_en') %></div>
            <% else %>
              <div class="brand-en"><%= I18n.t('company.name_cn') %></div>
            <% end %>
          </div>
          <h1><%= @purpose_text %></h1>
        </div>
        
        <p><%= I18n.t('verification_code.mailer.greeting') %></p>
        
        <p><%= I18n.t('verification_code.mailer.instruction', purpose: @purpose_text) %></p>
        
        <div class="code-box">
          <div class="code"><%= @code %></div>
        </div>
        
        <div class="warning">
          <strong><%= I18n.t('verification_code.mailer.important_notice') %></strong>
          <ul style="margin: 10px 0; padding-left: 20px;">
            <% I18n.t('verification_code.mailer.notice_items').each do |item| %>
              <li><%= item %></li>
            <% end %>
          </ul>
        </div>
        
        <p><%= I18n.t('verification_code.mailer.support') %></p>
        
        <div class="footer">
          <p><%= I18n.t('verification_code.mailer.footer_notice') %></p>
          <p><%= I18n.t('verification_code.mailer.copyright', 
                       year: Date.current.year, 
                       company: I18n.t('company.name')) %></p>
        </div>
      </div>
    <% end %>
  </body>
</html>