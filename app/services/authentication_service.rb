class AuthenticationService
  include ApiResponse

  attr_reader :user, :session, :errors

  def initialize
    @errors = []
  end

  # 用户注册
  def register(params, ip_address: nil, user_agent: nil)
    @user = User.new(params)

    if @user.save
      @session = Session.create_for_user!(
        @user,
        ip_address: ip_address,
        user_agent: user_agent
      )

      # 更新最后登录信息
      @user.update_last_login!(ip_address)

      ServiceResult.success(
        data: {
          user: @user,
          session: @session,
          token: @session.token,
          expires_at: @session.expires_at
        },
        message: I18n.t('auth.success.register')
      )
    else
      ServiceResult.validation_failure(@user.errors.full_messages)
    end
  end

  # 验证邮箱
  def verify_email(email, code)
    if VerificationCode.verify_code(email, code, 'email_verification')
      user = User.find_by(email: email)
      if user
        user.verify_email!
        ServiceResult.success(
          message: I18n.t('auth.success.email_verified')
        )
      else
        ServiceResult.not_found(I18n.t('services.errors.user_not_found'))
      end
    else
      ServiceResult.failure(
        message: I18n.t('verification_code.errors.invalid')
      )
    end
  end

  # 用户登录或注册（支持密码登录和验证码登录）
  def login_or_register(params, ip_address: nil, user_agent: nil)
    email = params[:email]
    password = params[:password]
    verification_code = params.delete(:verification_code)
    
    return ServiceResult.failure(message: I18n.t('auth.errors.email_empty')) if email.blank?
    
    # 密码和验证码至少需要提供一个
    if password.blank? && verification_code.blank?
      return ServiceResult.failure(message: I18n.t('auth.errors.password_or_code_required'))
    end
    
    email = email.downcase
    user = User.find_by(email: email)
    
    # 如果提供了验证码，优先使用验证码验证
    if verification_code.present?
      verification_result = VerificationCodeService.verify_code(
        email,
        verification_code,
        'email_verification'
      )
      
      unless verification_result.success?
        return verification_result
      end
      
      # 验证码验证成功
      if user
        # 用户存在，直接登录
        return login_user(user, ip_address, user_agent)
      else
        # 用户不存在，自动注册
        return auto_register(params.merge(email_verified: true), ip_address, user_agent)
      end
    end
    
    # 使用密码登录
    if password.present?
      if user&.authenticate(password)
        if user.active?
          if user.email_verified?
            return login_user(user, ip_address, user_agent)
          else
            return ServiceResult.failure(
              message: I18n.t('auth.errors.email_not_verified')
            )
          end
        else
          return ServiceResult.failure(
            message: I18n.t('auth.errors.account_disabled')
          )
        end
      else
        return ServiceResult.failure(
          message: I18n.t('auth.errors.invalid_credentials')
        )
      end
    end
    
    ServiceResult.failure(message: I18n.t('auth.errors.invalid_credentials'))
  end

  # 用户登录（保留原有的密码登录方法，用于其他地方调用）
  def login(email, password, ip_address: nil, user_agent: nil)
    return ServiceResult.failure(message: I18n.t('auth.errors.email_empty')) if email.blank?
    return ServiceResult.failure(message: I18n.t('auth.errors.password_empty')) if password.blank?

    user = User.find_by(email: email.downcase)

    if user&.authenticate(password)
      if user.active?
        if user.email_verified?
          return login_user(user, ip_address, user_agent)
        else
          return ServiceResult.failure(
            message: I18n.t('auth.errors.email_not_verified')
          )
        end
      else
        return ServiceResult.failure(
          message: I18n.t('auth.errors.account_disabled')
        )
      end
    else
      return ServiceResult.failure(
        message: I18n.t('auth.errors.invalid_credentials')
      )
    end
  end

  # 用户登出
  def logout(session_token)
    session = Session.find_by_token(session_token)

    if session
      session.revoke!
      ServiceResult.success(
        message: I18n.t('auth.success.logout')
      )
    else
      ServiceResult.failure(
        message: I18n.t('auth.errors.session_not_found')
      )
    end
  end

  # 登出所有设备
  def logout_all_devices(user)
    user.sessions.active.update_all(revoked_at: Time.current)
    ServiceResult.success(
      message: I18n.t('auth.success.logout_all')
    )
  end

  # 发送密码重置邮件
  def send_password_reset_email(email)
    user = User.find_by(email: email.downcase)

    if user
      user.generate_reset_password_token!
      # 发送密码重置验证码
      VerificationCode.create_for_email(email, 'password_reset')
      # TODO: 发送邮件
      ServiceResult.success(
        message: I18n.t('auth.success.password_reset_sent')
      )
    else
      # 为了安全，不暴露用户是否存在
      ServiceResult.success(
        message: I18n.t('auth.success.password_reset_sent')
      )
    end
  end

  # 重置密码
  def reset_password(email, code, new_password)
    if VerificationCode.verify_code(email, code, 'password_reset')
      user = User.find_by(email: email.downcase)

      if user&.reset_password_token_valid?
        if user.update(password: new_password)
          user.clear_reset_password_token!
          # 撤销所有现有会话
          user.sessions.active.update_all(revoked_at: Time.current)
          ServiceResult.success(
            message: I18n.t('auth.success.password_reset')
          )
        else
          ServiceResult.validation_failure(user.errors)
        end
      else
        ServiceResult.failure(
          message: I18n.t('auth.errors.invalid_token')
        )
      end
    else
      ServiceResult.failure(
        message: I18n.t('verification_code.errors.invalid')
      )
    end
  end

  # 更改密码
  def change_password(user, current_password, new_password)
    if user.authenticate(current_password)
      if user.update(password: new_password)
        # 撤销除当前会话外的所有会话
        ServiceResult.success(
          message: I18n.t('auth.success.password_changed')
        )
      else
        ServiceResult.validation_failure(user.errors)
      end
    else
      ServiceResult.failure(
        message: I18n.t('auth.errors.invalid_password')
      )
    end
  end

  # 通过会话令牌获取用户
  def self.authenticate_by_token(session_token)
    return nil if session_token.blank?

    session = Session.find_by_token(session_token)
    return nil unless session&.active?

    session.user
  end

  private

  # 执行用户登录
  def login_user(user, ip_address, user_agent)
    # 创建会话
    @session = Session.create_for_user!(
      user,
      ip_address: ip_address,
      user_agent: user_agent
    )

    # 更新最后登录信息
    user.update_last_login!(ip_address)

    ServiceResult.success(
      data: {
        user: user,
        session: @session,
        token: @session.token,
        expires_at: @session.expires_at
      },
      message: I18n.t('auth.success.login')
    )
  end

  # 自动注册用户
  def auto_register(params, ip_address, user_agent)
    # 设置默认密码（如果没有提供密码）
    if params[:password].blank?
      params[:password] = SecureRandom.urlsafe_base64(12)
    end
    
    @user = User.new(params)

    if @user.save
      # 登录用户
      login_user(@user, ip_address, user_agent)
    else
      ServiceResult.validation_failure(@user.errors.full_messages)
    end
  end


end