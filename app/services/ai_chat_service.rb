# AI聊天服务 - 处理与Moonshot API的交互
class AiChatService
  def initialize(chat_room, ai_session)
    @chat_room = chat_room
    @ai_session = ai_session
    @config = Rails.application.config.moonshot

    @client = OpenAI::Client.new
  end

  # 生成AI响应（使用流式响应）
  def generate_response(user_message, &block)
    generate_streaming_response(user_message, &block)
  end

  # 生成纯文本响应（适用于分析报告等场景）
  def generate_text_response(messages, stream: false, &block)
    begin
      # 如果messages不是数组，而是字符串，则将其包装成用户消息
      messages_array = if messages.is_a?(String)
        [{ role: "user", content: messages }]
      else
        messages # 假设已经是包含system和user消息的数组
      end
      
      # 调用Moonshot API
      response = @client.chat(
        parameters: {
          model: @config[:default_model],
          messages: messages_array,
          temperature: @config[:model_config][:temperature],
          max_tokens: @config[:model_config][:max_tokens],
          stream: stream ? proc do |chunk, _event|
            if chunk.dig("choices", 0, "delta", "content")
              content_chunk = chunk.dig("choices", 0, "delta", "content")
              block.call(content_chunk) if block_given?
            end
          end : false
        }
      )

      unless stream
        content = response.dig("choices", 0, "message", "content")
        usage = response["usage"] || {}
        ServiceResult.success(
          data: {
            content: content,
            model_name: @config[:default_model],
            prompt_tokens: usage["prompt_tokens"] || 0,
            completion_tokens: usage["completion_tokens"] || 0,
            total_tokens: usage["total_tokens"] || 0,
            metadata: { streaming: false }
          }
        )
      end
    rescue => e
      Rails.logger.error "Moonshot text generation error: #{e.message}"
      handle_api_error(e)
    end
  end

  # 流式生成响应（用于实时流式输出）
  def generate_streaming_response(user_message, &block)
    begin
      # 构建对话上下文
      messages = build_conversation_context(user_message)

      # 获取模型配置
      model_config = @ai_session.model_config.with_indifferent_access
      default_config = @config[:model_config]

      Rails.logger.info "Sending streaming request to Moonshot API with #{messages.length} messages"

      # 使用OpenAI客户端进行流式响应
      accumulated_content = ""
      response_metadata = {}

      @client.chat(
        parameters: {
          model: @config[:default_model],
          messages: messages,
          temperature: model_config[:temperature] || default_config[:temperature],
          max_tokens: model_config[:max_tokens] || default_config[:max_tokens],
          top_p: model_config[:top_p] || default_config[:top_p],
          frequency_penalty: model_config[:frequency_penalty] || default_config[:frequency_penalty],
          presence_penalty: model_config[:presence_penalty] || default_config[:presence_penalty],
          stream: proc do |chunk, _event|
            if chunk.dig("choices", 0, "delta", "content")
              content_chunk = chunk.dig("choices", 0, "delta", "content")
              accumulated_content += content_chunk

              Rails.logger.info "AI响应内容块: '#{content_chunk}' (长度: #{content_chunk.length})"

              # 调用回调函数处理流式数据
              if block_given?
                Rails.logger.info "调用流式回调处理AI响应内容"
                block.call(content_chunk, accumulated_content)
              end
            end

            # 提取元数据
            if chunk['id']
              response_metadata[:response_id] = chunk['id']
            end

            if chunk['created']
              response_metadata[:created_at] = chunk['created']
            end

            if chunk.dig("choices", 0, "finish_reason")
              response_metadata[:finish_reason] = chunk.dig("choices", 0, "finish_reason")
            end

            if chunk['usage']
              response_metadata[:usage] = chunk['usage']
            end
          end
        }
      )

      # 构建响应结果
      result_data = build_response_result(accumulated_content, response_metadata)
      
      ServiceResult.success(
        data: result_data,
        message: "AI响应生成成功"
      )

    rescue => e
      Rails.logger.error "Moonshot streaming API error: #{e.message}"
      handle_api_error(e)
    end
  end

  # 获取模型列表
  def self.available_models
    ServiceResult.success(
      data: {
        models: [
          {
            id: 'moonshot-v1-128k-vision-preview',
            name: 'Moonshot V1 128K Vision Preview',
            description: 'Kimi大模型，支持长文本和视觉理解，128K上下文窗口',
            max_tokens: 128000,
            pricing: { prompt: 0.012, completion: 0.012 }
          }
        ]
      }
    )
  end

  private

  # 构建响应结果
  def build_response_result(content, metadata)
    # 如果没有从API获取到token统计，使用估算
    usage = metadata[:usage] || {}
    prompt_tokens = usage['prompt_tokens'] || OpenAI.rough_token_count(build_conversation_context(@chat_room.chat_messages.last).map { |m| m[:content] }.join(" "))
    completion_tokens = usage['completion_tokens'] || OpenAI.rough_token_count(content)
    total_tokens = usage['total_tokens'] || (prompt_tokens + completion_tokens)

    # 确保有内容返回
    if content.blank?
      Rails.logger.warn "Moonshot API returned empty content"
      content = "抱歉，AI服务没有返回内容，请重试。"
      completion_tokens = OpenAI.rough_token_count(content)
      total_tokens = prompt_tokens + completion_tokens
    end

    Rails.logger.info "Final accumulated content length: #{content.length}"

    {
      content: content,
      model_name: @config[:default_model],
      prompt_tokens: prompt_tokens,
      completion_tokens: completion_tokens,
      total_tokens: total_tokens,
      metadata: {
        streaming: true,
        finish_reason: metadata[:finish_reason] || 'stop',
        response_id: metadata[:response_id],
        created_at: metadata[:created_at]
      }
    }
  end

  def build_conversation_context(user_message)
    # 获取最近的对话历史（最多20条消息）
    recent_messages = @chat_room.chat_messages
                                .where(status: :sent)
                                .order(:created_at)
                                .last(20)

    # 构建系统提示
    messages = [
      {
        role: 'system',
        content: build_system_prompt
      }
    ]

    # 添加历史对话
    recent_messages.each do |msg|
      next if msg.id == user_message.id # 跳过当前用户消息
      next if msg.content.blank? # 跳过空内容消息

      role = case msg.message_type
             when 'user' then 'user'
             when 'ai' then 'assistant'
             else next # 跳过系统消息
             end

      messages << {
        role: role,
        content: msg.content
      }
    end

    # 添加当前用户消息
    if user_message.content.present?
      messages << {
        role: 'user',
        content: user_message.content
      }
    else
      Rails.logger.warn "User message #{user_message.id} has empty content"
      # 提供一个默认消息，避免API调用失败
      messages << {
        role: 'user',
        content: '请说点什么。'
      }
    end

    Rails.logger.info "Built conversation context with #{messages.length} messages"
    messages.each_with_index do |msg, idx|
      Rails.logger.debug "Message #{idx}: #{msg[:role]} - #{msg[:content]&.length || 0} characters"
    end

    messages
  end

  def build_system_prompt
    default_prompt = "你是Kimi智能助手，由月之暗面科技有限公司开发的人工智能助手。你能够阅读和理解长文本内容，支持多种文件格式，擅长中英文对话。请以友好、专业、准确的方式回答用户的问题，提供有价值的帮助。"

    # 可以根据聊天室或用户偏好自定义系统提示
    @ai_session.model_config.dig('system_prompt') || default_prompt
  end

  def handle_api_error(error)
    case error.class.name
    when 'Net::TimeoutError'
      ServiceResult.failure(message: "AI服务响应超时，请稍后重试")
    when 'HTTParty::ResponseError'
      ServiceResult.failure(message: "AI服务连接失败，请检查网络连接")
    else
      if error.message.include?('401')
        ServiceResult.failure(message: "API认证失败，请检查API密钥配置")
      elsif error.message.include?('429')
        ServiceResult.failure(message: "API调用频率超限，请稍后重试")
      elsif error.message.include?('quota')
        ServiceResult.failure(message: "API配额已用完，请检查账户余额")
      else
        ServiceResult.failure(message: "AI服务错误：#{error.message}")
      end
    end
  end
end