# 统一API响应结构
module ApiResponse
  extend ActiveSupport::Concern

  # 成功响应
  def success_response(data: nil, message: nil, meta: nil, status: :ok)
    response = {
      success: true,
      code: response_code_for_status(status),
      message: message
    }
    
    response[:data] = data if data.present?
    response[:meta] = meta if meta.present?
    response[:timestamp] = Time.current.iso8601
    
    response
  end

  # 错误响应
  def error_response(message:, details: nil, code: nil, status: :bad_request)
    response = {
      success: false,
      code: code || response_code_for_status(status),
      message: message
    }
    
    response[:details] = normalize_error_details(details) if details.present?
    response[:timestamp] = Time.current.iso8601
    
    response
  end

  # 分页响应
  def paginated_response(data:, pagination:, message: nil, status: :ok)
    success_response(
      data: data,
      message: message,
      meta: { pagination: pagination },
      status: status
    )
  end

  # 验证错误响应
  def validation_error_response(errors, message: nil)
    error_response(
      message: message || I18n.t('errors.validation_failed'),
      details: normalize_validation_errors(errors),
      status: :unprocessable_content
    )
  end

  # 未找到资源响应
  def not_found_response(resource_name = nil)
    message = if resource_name
                I18n.t('errors.resource_not_found', resource: resource_name)
              else
                I18n.t('errors.not_found')
              end
    
    error_response(
      message: message,
      status: :not_found
    )
  end

  # 未授权响应
  def unauthorized_response(message: nil)
    error_response(
      message: message || I18n.t('errors.unauthorized'),
      status: :unauthorized
    )
  end

  # 禁止访问响应
  def forbidden_response(message: nil)
    error_response(
      message: message || I18n.t('errors.forbidden'),
      status: :forbidden
    )
  end

  # 服务器错误响应
  def internal_error_response(message: nil)
    error_response(
      message: message || I18n.t('errors.internal_server_error'),
      status: :internal_server_error
    )
  end

  private

  # 根据HTTP状态码生成响应码
  def response_code_for_status(status)
    case status.to_sym
    when :ok, :success
      200
    when :created
      201
    when :accepted
      202
    when :no_content
      204
    when :bad_request
      400
    when :unauthorized
      401
    when :forbidden
      403
    when :not_found
      404
    when :method_not_allowed
      405
    when :conflict
      409
    when :unprocessable_entity
      422
    when :too_many_requests
      429
    when :internal_server_error
      500
    when :bad_gateway
      502
    when :service_unavailable
      503
    else
      500
    end
  end

  # 标准化错误详情
  def normalize_error_details(details)
    case details
    when Array
      details.map(&:to_s)
    when Hash
      details.transform_values { |v| Array.wrap(v).map(&:to_s) }
    when String
      [details]
    when ActiveModel::Errors
      normalize_validation_errors(details)
    else
      [details.to_s]
    end
  end

  # 标准化验证错误
  def normalize_validation_errors(errors)
    case errors
    when ActiveModel::Errors
      errors.to_hash.transform_values { |v| Array.wrap(v) }
    when Hash
      errors.transform_values { |v| Array.wrap(v) }
    when Array
      { base: errors }
    else
      { base: [errors.to_s] }
    end
  end
end