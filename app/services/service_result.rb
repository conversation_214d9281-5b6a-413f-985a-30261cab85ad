# Service结果包装器
class ServiceResult
  attr_reader :success, :data, :message, :errors, :meta

  def initialize(success:, data: nil, message: nil, errors: nil, meta: nil)
    @success = success
    @data = data
    @message = message
    @errors = errors
    @meta = meta
  end

  def success?
    @success
  end

  def failure?
    !@success
  end

  def error_details
    return nil if success?
    
    case @errors
    when ActiveModel::Errors
      @errors.to_hash.transform_values { |v| Array.wrap(v) }
    when Hash
      @errors.transform_values { |v| Array.wrap(v) }
    when Array
      { base: @errors }
    when String
      { base: [@errors] }
    else
      { base: [@errors.to_s] } if @errors
    end
  end

  # 类方法：创建成功结果
  def self.success(data: nil, message: nil, meta: nil)
    new(success: true, data: data, message: message, meta: meta)
  end

  # 类方法：创建失败结果
  def self.failure(message:, errors: nil, meta: nil)
    new(success: false, message: message, errors: errors, meta: meta)
  end

  # 类方法：创建验证失败结果
  def self.validation_failure(errors, message: nil)
    new(
      success: false,
      message: message || I18n.t('errors.validation_failed'),
      errors: errors
    )
  end

  # 类方法：创建未找到结果
  def self.not_found(resource_name = nil)
    message = if resource_name
                I18n.t('errors.resource_not_found', resource: resource_name)
              else
                I18n.t('errors.not_found')
              end
    
    new(success: false, message: message)
  end

  # 类方法：创建未授权结果
  def self.unauthorized(message: nil)
    new(
      success: false,
      message: message || I18n.t('errors.unauthorized')
    )
  end

  # 类方法：创建禁止访问结果
  def self.forbidden(message: nil)
    new(
      success: false,
      message: message || I18n.t('errors.forbidden')
    )
  end

  # 将ServiceResult转换为API响应格式
  def to_api_response(status: nil)
    if success?
      status ||= :ok
      response = {
        success: true,
        code: status_code_for(status),
        message: @message
      }
      response[:data] = @data if @data.present?
      response[:meta] = @meta if @meta.present?
    else
      status ||= :bad_request
      response = {
        success: false,
        code: status_code_for(status),
        message: @message
      }
      response[:details] = error_details if error_details.present?
    end
    
    response[:timestamp] = Time.current.iso8601
    response
  end

  private

  def status_code_for(status)
    case status.to_sym
    when :ok, :success then 200
    when :created then 201
    when :accepted then 202
    when :no_content then 204
    when :bad_request then 400
    when :unauthorized then 401
    when :forbidden then 403
    when :not_found then 404
    when :conflict then 409
    when :unprocessable_entity then 422
    when :too_many_requests then 429
    when :internal_server_error then 500
    else 500
    end
  end
end