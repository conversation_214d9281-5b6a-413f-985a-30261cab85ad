class AccountDeletionService
  include ApiResponse

  # 删除用户账户及所有相关数据
  def delete_account(user)
    begin
      ActiveRecord::Base.transaction do
        # 删除所有相关数据
        delete_user_related_data(user)
        
        # 最后删除用户记录
        user.destroy!
        
        Rails.logger.info "Account deleted for user: #{user.email} (ID: #{user.id})"
      end

      ServiceResult.success(
        message: I18n.t('auth.success.account_deleted')
      )
    rescue StandardError => e
      Rails.logger.error "Failed to delete account for user #{user.id}: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      
      ServiceResult.failure(
        message: I18n.t('auth.errors.account_deletion_failed')
      )
    end
  end

  private

  def delete_user_related_data(user)
    # 删除用户会话
    user.sessions.destroy_all
    
    # 删除AI聊天会话
    user.ai_chat_sessions.destroy_all
    
    # 删除聊天消息
    user.chat_messages.destroy_all
    
    # 删除聊天室参与记录
    user.chat_room_participants.destroy_all
    
    # 删除用户创建的聊天室
    user.chat_rooms.destroy_all
    
    # 这里可以添加更多相关数据的删除逻辑
    # 例如：用户上传的文件、用户的健身记录等
    
    Rails.logger.info "Deleted related data for user: #{user.email} (ID: #{user.id})"
  end
end