class UserManagementService
  include Pagy::Backend
  
  attr_reader :errors

  def initialize
    @errors = []
  end

  # 获取用户列表（分页）
  def list_users(params = {})
    users = User.all
    
    # 搜索过滤
    if params[:search].present?
      search_term = "%#{params[:search]}%"
      users = users.where(
        'email ILIKE ? OR name ILIKE ?',
        search_term, search_term
      )
    end

    # 角色过滤
    if params[:role].present?
      users = users.with_role(params[:role])
    end

    # 状态过滤
    if params[:status].present?
      users = users.where(status: params[:status])
    end

    # 邮箱验证状态过滤
    case params[:email_verified]
    when 'true'
      users = users.verified
    when 'false'
      users = users.unverified
    end

    # 排序
    case params[:sort_by]
    when 'email'
      users = users.order(:email)
    when 'name'
      users = users.order(:name)
    when 'created_at'
      users = users.order(:created_at)
    when 'last_login_at'
      users = users.order(last_login_at: :desc)
    else
      users = users.order(:id)
    end

    # 分页
    page = params[:page] || 1
    per_page = params[:per_page] || 20
    
    pagy, paginated_users = pagy(users, page: page, limit: per_page)
    
    ServiceResult.success(
      data: { users: paginated_users },
      meta: {
        pagination: {
          current_page: pagy.page,
          total_pages: pagy.pages,
          total_count: pagy.count,
          per_page: pagy.limit
        }
      }
    )
  end

  # 获取单个用户详情
  def get_user(id)
    user = User.find_by(id: id)
    
    if user
      ServiceResult.success(
        data: {
          user: user,
          sessions: user.sessions.active.order(created_at: :desc)
        }
      )
    else
      ServiceResult.not_found(I18n.t('services.errors.user_not_found'))
    end
  end

  # 创建用户（管理员操作）
  def create_user(params)
    user = User.new(user_params(params))
    
    if params[:skip_email_verification] == 'true'
      user.email_verified_at = Time.current
    end
    
    if user.save
      ServiceResult.success(
        data: { user: user },
        message: I18n.t('users.success.created')
      )
    else
      ServiceResult.validation_failure(user.errors)
    end
  end

  # 更新用户信息
  def update_user(id, params)
    user = User.find_by(id: id)
    
    return ServiceResult.not_found(I18n.t('services.errors.user_not_found')) unless user
    
    if user.update(user_update_params(params))
      ServiceResult.success(
        data: { user: user },
        message: I18n.t('users.success.updated')
      )
    else
      ServiceResult.validation_failure(user.errors)
    end
  end

  # 更新用户角色
  def update_user_roles(id, roles)
    user = User.find_by(id: id)
    
    return ServiceResult.not_found(I18n.t('services.errors.user_not_found')) unless user
    
    roles_array = Array.wrap(roles).map(&:to_s) & User::AVAILABLE_ROLES
    if roles_array.empty?
      return ServiceResult.failure(message: I18n.t('users.errors.invalid_roles'))
    end
    
    if user.set_roles(roles_array)
      ServiceResult.success(
        data: { user: user },
        message: I18n.t('users.success.roles_updated')
      )
    else
      ServiceResult.validation_failure(user.errors)
    end
  end

  # 添加用户角色
  def add_user_role(id, role)
    user = User.find_by(id: id)
    
    return ServiceResult.not_found(I18n.t('services.errors.user_not_found')) unless user
    
    if user.add_role(role)
      ServiceResult.success(
        data: { user: user },
        message: I18n.t('users.success.role_added', role: role)
      )
    else
      ServiceResult.failure(
        message: I18n.t('users.errors.invalid_role_or_already_exists', role: role)
      )
    end
  end

  # 移除用户角色
  def remove_user_role(id, role)
    user = User.find_by(id: id)
    
    return ServiceResult.not_found(I18n.t('services.errors.user_not_found')) unless user
    
    if user.remove_role(role)
      ServiceResult.success(
        data: { user: user },
        message: I18n.t('users.success.role_removed', role: role)
      )
    else
      ServiceResult.failure(
        message: I18n.t('users.errors.role_not_found_or_last_role', role: role)
      )
    end
  end

  # 更新用户状态
  def update_user_status(id, status)
    user = User.find_by(id: id)
    
    return ServiceResult.not_found(I18n.t('services.errors.user_not_found')) unless user
    
    if User.statuses.keys.include?(status)
      user.update!(status: status)
      
      # 如果用户被禁用，撤销所有会话
      if status == 'inactive' || status == 'suspended'
        user.sessions.active.update_all(revoked_at: Time.current)
      end
      
      ServiceResult.success(
        data: { user: user },
        message: I18n.t('users.success.status_updated')
      )
    else
      ServiceResult.failure(
        message: I18n.t('users.errors.invalid_status')
      )
    end
  end

  # 删除用户
  def delete_user(id)
    user = User.find_by(id: id)
    
    return ServiceResult.not_found(I18n.t('services.errors.user_not_found')) unless user
    
    # 不能删除最后一个管理员
    if user.admin? && User.admins.count == 1
      return ServiceResult.failure(
        message: I18n.t('users.errors.cannot_delete_last_admin')
      )
    end
    
    user.destroy!
    ServiceResult.success(
      message: I18n.t('users.success.deleted')
    )
  end

  # 重置用户密码（管理员操作）
  def reset_user_password(id, new_password)
    user = User.find_by(id: id)
    
    return ServiceResult.not_found(I18n.t('services.errors.user_not_found')) unless user
    
    if user.update(password: new_password)
      # 撤销用户所有会话
      user.sessions.active.update_all(revoked_at: Time.current)
      ServiceResult.success(
        message: I18n.t('users.success.password_reset')
      )
    else
      ServiceResult.validation_failure(user.errors)
    end
  end

  # 强制邮箱验证
  def verify_user_email(id)
    user = User.find_by(id: id)
    
    return ServiceResult.not_found(I18n.t('services.errors.user_not_found')) unless user
    
    user.verify_email!
    ServiceResult.success(
      message: I18n.t('users.success.email_verified')
    )
  end

  # 撤销用户所有会话
  def revoke_user_sessions(id)
    user = User.find_by(id: id)
    
    return ServiceResult.not_found(I18n.t('services.errors.user_not_found')) unless user
    
    revoked_count = user.sessions.active.count
    user.sessions.active.update_all(revoked_at: Time.current)
    
    ServiceResult.success(
      message: I18n.t('users.success.sessions_revoked', count: revoked_count)
    )
  end

  # 获取用户统计信息
  def get_user_statistics
    ServiceResult.success(
      data: {
        statistics: {
          total_users: User.count,
          active_users: User.active.count,
          verified_users: User.verified.count,
          admin_users: User.admins.count,
          recent_registrations: User.where('created_at >= ?', 7.days.ago).count,
          recent_logins: User.where('last_login_at >= ?', 24.hours.ago).count
        }
      }
    )
  end

  private

  def user_params(params)
    params.permit(:email, :password, :name, roles: [])
  end

  def user_update_params(params)
    allowed_params = [:name, :email]
    
    # 只有管理员可以修改角色和状态
    if params[:current_user]&.admin?
      allowed_params += [:status]
      return params.permit(*allowed_params, roles: [])
    end
    
    params.permit(*allowed_params)
  end
end