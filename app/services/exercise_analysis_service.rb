# 运动分析服务 - 封装运动分析相关的业务逻辑
class ExerciseAnalysisService
  include Pagy::Backend
  
  attr_reader :errors
  
  def initialize
    @errors = []
  end
  
  # 创建运动分析请求
  def create_analysis(user, images, params = {})
    # 事务管理，确保数据一致性
    analysis = nil
    ActiveRecord::Base.transaction do
      # 创建分析记录
      analysis = ExerciseAnalysis.create!(
        user: user,
        status: :pending,
        analysis_result: nil
      )

      # 附加图片，优化处理：如果blob已存在则直接关联，避免mismatched digest问题
      images.each do |_,image|
        # image 类型确定为 ActionDispatch::Http::UploadedFile
        # 计算文件摘要，用于检查是否已存在相同的blob
        image.rewind
        checksum = Digest::MD5.base64digest(image.read)
        image.rewind
        
        # 查找是否存在相同的blob
        blob = ActiveStorage::Blob.find_by(checksum: checksum)
        
        if blob
          # 如果存在，直接关联现有blob
          analysis.images.attach(blob)
        else
          # 如果不存在，创建新的blob并关联
          analysis.images.attach(io: image, filename: image.original_filename, content_type: image.content_type)
        end
      end
      
      # 记录用户活动
      self.class.log_user_activity(user, 'create_exercise_analysis', { analysis_id: analysis.id })
      
    end
    
    ServiceResult.success(data: analysis)
  rescue StandardError => e
    Rails.logger.error "创建运动分析失败: #{e.message}"
    Rails.logger.error e.backtrace.join("\n")
    ServiceResult.failure(message: "创建运动分析失败，请重试")
  end

  # 获取用户的分析历史
  def get_analysis_history(user, params = {})
    analyses = ExerciseAnalysis.where(user: user)
                              .order(created_at: :desc)
    
    # 如果提供了日期参数，过滤出该日期的分析记录
    if params[:date].present?
      begin
        target_date = DateTime.parse(params[:date])
        start_time = target_date.beginning_of_day
        end_time = target_date.end_of_day
        analyses = analyses.where(created_at: start_time..end_time)
      rescue ArgumentError => e
        Rails.logger.error "日期格式错误: #{e.message}"
        # 尝试作为普通日期解析
        begin
          target_date = Date.parse(params[:date])
          analyses = analyses.where(created_at: target_date.beginning_of_day..target_date.end_of_day)
        rescue ArgumentError => e2
          Rails.logger.error "无法解析日期: #{e2.message}"
          # 继续使用未过滤的查询结果
        end
      end
    end
    
    # 分页
    page = params[:page] || 1
    per_page = params[:per_page] || 10
    
    pagy, paginated_analyses = pagy(analyses, page: page, limit: per_page)
    
    ServiceResult.success(
      data: { analyses: paginated_analyses },
      meta: {
        pagination: {
          current_page: pagy.page,
          total_pages: pagy.pages,
          total_count: pagy.count,
          per_page: pagy.limit
        }
      }
    )
  rescue StandardError => e
    Rails.logger.error "获取分析历史失败: #{e.message}"
    ServiceResult.failure(message: "获取分析历史失败，请重试")
  end

  # 获取单个分析详情
  def get_analysis_detail(user, analysis_id)
    analysis = ExerciseAnalysis.find_by(id: analysis_id, user: user)
    
    if analysis
      ServiceResult.success(data: analysis)
    else
      ServiceResult.not_found(message: "未找到该分析记录")
    end
  rescue StandardError => e
    Rails.logger.error "获取分析详情失败: #{e.message}"
    ServiceResult.failure(message: "获取分析详情失败，请重试")
  end

  # 删除分析记录
  def delete_analysis(user, analysis_id)
    analysis = ExerciseAnalysis.find_by(id: analysis_id, user: user)
    
    if analysis
        analysis.destroy
        # 记录用户活动
        self.class.log_user_activity(user, 'delete_exercise_analysis', { analysis_id: analysis_id })
      ServiceResult.success(message: "分析记录已删除")
    else
      ServiceResult.not_found(message: "未找到该分析记录")
    end
  rescue StandardError => e
    Rails.logger.error "删除分析记录失败: #{e.message}"
    ServiceResult.failure(message: "删除分析记录失败，请重试")
  end

  private

  # 记录用户活动
  def self.log_user_activity(user, action, details = {})
    # 这里可以实现用户活动日志记录的逻辑
    # 例如写入数据库或发送到监控系统
    Rails.logger.info "用户 #{user.id} 执行动作: #{action}, 详情: #{details}"
  end
end