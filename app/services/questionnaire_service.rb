class QuestionnaireService
  include ApiResponse
  
  class << self
    # 更新用户问卷信息
    def update_user_questionnaire(user, questionnaire_data)
      begin
        user.update!(
          exercise_frequency: questionnaire_data[:exercise_frequency],
          fitness_goals: Array.wrap(questionnaire_data[:fitness_goals]).compact.uniq,
          obstacles: Array.wrap(questionnaire_data[:obstacles]).compact.uniq
        )
        
        ServiceResult.success(
          data: { user: user },
          message: I18n.t('questionnaire.success.updated')
        )
      rescue ActiveRecord::RecordInvalid => e
        ServiceResult.error(
          message: I18n.t('questionnaire.errors.update_failed'),
          details: e.record.errors.full_messages
        )
      rescue => e
        Rails.logger.error "Questionnaire update failed: #{e.message}"
        ServiceResult.error(
          message: I18n.t('questionnaire.errors.update_failed')
        )
      end
    end
    
    # 创建用户并保存问卷数据（用于注册流程）
    def create_user_with_questionnaire(user_params, ip_address: nil, user_agent: nil)
      ActiveRecord::Base.transaction do
        # 创建用户
        user = User.new(user_params.except(:fitness_goals, :obstacles))
        user.fitness_goals = Array.wrap(user_params[:fitness_goals]).compact.uniq
        user.obstacles = Array.wrap(user_params[:obstacles]).compact.uniq
        
        # 如果没有密码，生成一个随机密码（验证码注册）
        if user.password.blank?
          user.password = SecureRandom.urlsafe_base64(12)
        end
        
        user.save!
        
        # 创建会话令牌
        session = user.sessions.create!(
          ip_address: ip_address,
          user_agent: user_agent,
          expires_at: 30.days.from_now
        )
        
        # 更新最后登录信息
        user.update_last_login!(ip_address) if ip_address
        
        ServiceResult.success(
          data: {
            user: user,
            token: session.token,
            expires_at: session.expires_at
          },
          message: I18n.t('questionnaire.success.registered_with_questionnaire')
        )
      end
    rescue ActiveRecord::RecordInvalid => e
      ServiceResult.error(
        message: I18n.t('questionnaire.errors.registration_failed'),
        details: e.record.errors.full_messages
      )
    rescue => e
      Rails.logger.error "User registration with questionnaire failed: #{e.message}"
      ServiceResult.error(
        message: I18n.t('questionnaire.errors.registration_failed')
      )
    end
    
    # 验证问卷数据完整性
    def validate_questionnaire_data(questionnaire_data)
      errors = []
      
      # 验证运动频率
      unless ['occasionally', 'regular', 'athlete'].include?(questionnaire_data[:exercise_frequency])
        errors << I18n.t('questionnaire.validations.invalid_exercise_frequency')
      end
      
      # 验证健身目标
      fitness_goals = Array.wrap(questionnaire_data[:fitness_goals]).compact
      if fitness_goals.empty?
        errors << I18n.t('questionnaire.validations.fitness_goals_required')
      end
      
      valid_goals = %w[weight_loss muscle_gain endurance strength flexibility health stress_relief competition]
      invalid_goals = fitness_goals - valid_goals
      if invalid_goals.any?
        errors << I18n.t('questionnaire.validations.invalid_fitness_goals', goals: invalid_goals.join(', '))
      end
      
      # 验证阻碍因素
      obstacles = Array.wrap(questionnaire_data[:obstacles]).compact
      if obstacles.empty?
        errors << I18n.t('questionnaire.validations.obstacles_required')
      end
      
      valid_obstacles = %w[lack_consistency unhealthy_work_habits lack_support busy_schedule lack_motivation financial_constraints physical_limitations lack_knowledge]
      invalid_obstacles = obstacles - valid_obstacles
      if invalid_obstacles.any?
        errors << I18n.t('questionnaire.validations.invalid_obstacles', obstacles: invalid_obstacles.join(', '))
      end
      
      if errors.any?
        ServiceResult.error(
          message: I18n.t('questionnaire.validations.invalid_data'),
          details: errors
        )
      else
        ServiceResult.success
      end
    end
    
    # 获取问卷完成统计
    def questionnaire_statistics
      total_users = User.count
      completed_users = User.questionnaire_completed.count
      completion_rate = total_users > 0 ? (completed_users.to_f / total_users * 100).round(2) : 0
      
      # 运动频率分布
      frequency_stats = User.group(:exercise_frequency).count
      
      # 热门健身目标
      goals_stats = {}
      User.where.not(fitness_goals: [nil, '']).find_each do |user|
        user.fitness_goals_array.each do |goal|
          goals_stats[goal] = (goals_stats[goal] || 0) + 1
        end
      end
      
      # 常见阻碍因素
      obstacles_stats = {}
      User.where.not(obstacles: [nil, '']).find_each do |user|
        user.obstacles_array.each do |obstacle|
          obstacles_stats[obstacle] = (obstacles_stats[obstacle] || 0) + 1
        end
      end
      
      ServiceResult.success(
        data: {
          total_users: total_users,
          completed_users: completed_users,
          completion_rate: completion_rate,
          frequency_distribution: frequency_stats,
          popular_goals: goals_stats.sort_by { |_, count| -count }.to_h,
          common_obstacles: obstacles_stats.sort_by { |_, count| -count }.to_h
        }
      )
    end
  end
end