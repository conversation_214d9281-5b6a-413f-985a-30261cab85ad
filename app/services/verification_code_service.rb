class VerificationCodeService
  class << self
    # 发送验证码
    def send_code(email, purpose = 'email_verification', locale = I18n.default_locale)
      I18n.with_locale(locale) do
        # 检查发送频率
        if recently_sent?(email, purpose)
          return ServiceResult.failure(
            message: I18n.t('verification_code.errors.too_frequent')
          )
        end

        begin
          # 创建验证码
          verification_code = VerificationCode.create_for_email(email, purpose)
          
          # 发送邮件（异步处理）
          VerificationCodeMailer.send_verification_code(
            email, 
            verification_code.code, 
            purpose,
            locale
          ).deliver_later(queue: 'mailers')

          ServiceResult.success(
            data: { expires_at: verification_code.expires_at },
            message: I18n.t('verification_code.success.sent')
          )
        rescue => e
          Rails.logger.error "Failed to send verification code: #{e.message}"
          ServiceResult.failure(
            message: I18n.t('verification_code.errors.send_failed')
          )
        end
      end
    end

    # 验证验证码
    def verify_code(email, code, purpose = 'email_verification', locale = I18n.default_locale)
      I18n.with_locale(locale) do
        if VerificationCode.verify_code(email, code, purpose)
          ServiceResult.success(
            message: I18n.t('verification_code.success.verified')
          )
        else
          ServiceResult.failure(
            message: I18n.t('verification_code.errors.invalid')
          )
        end
      end
    end

    # 清理过期验证码
    def cleanup_expired_codes
      deleted_count = VerificationCode.where('expires_at < ?', Time.current).delete_all
      ServiceResult.success(
        data: { deleted_count: deleted_count },
        message: "已清理 #{deleted_count} 个过期验证码"
      )
    end

    private

    # 检查最近是否发送过验证码
    def recently_sent?(email, purpose)
      VerificationCode
        .where(email: email, code_type: purpose)
        .where('created_at > ?', 1.minute.ago)
        .exists?
    end
  end
end