# Action Cable 连接类 - 处理WebSocket连接认证
module ApplicationCable
  class Connection < ActionCable::Connection::Base
    identified_by :current_user

    def connect
      self.current_user = find_verified_user
      logger.add_tags 'ActionCable', current_user.email
      logger.info "User #{current_user.id} connected via WebSocket"
    end

    private

    # 验证用户身份
    def find_verified_user
      # 从查询参数或头部获取认证令牌
      token = request.params[:token] || 
              request.headers['Authorization']&.split(' ')&.last

      if token.blank?
        reject_unauthorized_connection
      end

      begin

        # 直接使用session token查找用户（不是JWT）
        session = Session.active.find_by(token: token)
        if session&.user&.active?
          logger.info "WebSocket authentication successful for user: #{session.user.email}"
          session.user
        else
          logger.error "WebSocket authentication failed: Invalid or expired session token"
          reject_unauthorized_connection
        end
      rescue => e
        logger.error "WebSocket authentication failed: #{e.message}"
        reject_unauthorized_connection
      end
    end
  end
end