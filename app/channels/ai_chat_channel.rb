# AI聊天频道 - 处理WebSocket连接和实时消息推送
class AiChatChannel < ApplicationCable::Channel
  # 订阅特定聊天室的频道
  def subscribed
    # 验证用户身份
    reject unless current_user
    
    chat_room_id = params[:chat_room_id]
    chat_room = ChatRoom.find_by(id: chat_room_id)
    
    # 验证聊天室存在且用户有权限访问
    if chat_room && chat_room.participant?(current_user)
      stream_from "chat_room_#{chat_room_id}"
      
      # 记录用户连接
      Rails.logger.info "User #{current_user.id} subscribed to chat room #{chat_room_id}"
      
      # 广播用户上线消息（可选）
      broadcast_user_status('online')
    else
      reject
    end
  end

  # 取消订阅
  def unsubscribed
    if @chat_room_id
      Rails.logger.info "User #{current_user&.id} unsubscribed from chat room #{@chat_room_id}"
      
      # 广播用户下线消息（可选）
      broadcast_user_status('offline')
    end
  end

  # 处理客户端发送的消息
  def receive(data)
    case data['action']
    when 'typing_start'
      broadcast_typing_status(true)
    when 'typing_stop'
      broadcast_typing_status(false)
    when 'message_read'
      mark_message_as_read(data['message_id'])
    end
  end

  private

  def chat_room_id
    @chat_room_id ||= params[:chat_room_id]
  end

  def chat_room
    @chat_room ||= ChatRoom.find_by(id: chat_room_id)
  end

  # 广播用户在线状态
  def broadcast_user_status(status)
    ActionCable.server.broadcast(
      "chat_room_#{chat_room_id}",
      {
        type: 'user_status',
        user: {
          id: current_user.id,
          name: current_user.name,
          status: status
        },
        timestamp: Time.current.iso8601
      }
    )
  end

  # 广播打字状态
  def broadcast_typing_status(is_typing)
    ActionCable.server.broadcast(
      "chat_room_#{chat_room_id}",
      {
        type: 'typing_status',
        user: {
          id: current_user.id,
          name: current_user.name
        },
        is_typing: is_typing,
        timestamp: Time.current.iso8601
      }
    )
  end

  # 标记消息为已读
  def mark_message_as_read(message_id)
    message = ChatMessage.find_by(id: message_id, chat_room: chat_room)
    return unless message

    # 这里可以实现消息已读状态的逻辑
    # 例如创建一个 MessageReadStatus 模型来跟踪已读状态
    
    Rails.logger.info "User #{current_user.id} read message #{message_id}"
  end
end