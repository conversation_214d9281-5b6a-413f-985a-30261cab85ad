# == Schema Information
#
# Table name: chat_rooms(聊天室表 - 存储聊天室基本信息和配置)
#
#  id                                                     :bigint           not null, primary key
#  description(聊天室描述)                                :text
#  name(聊天室名称)                                       :string(100)      not null
#  status(聊天室状态：0-活跃，1-非活跃)                   :integer          default("active"), not null
#  created_at(created_at: 创建时间, updated_at: 更新时间) :datetime         not null
#  updated_at(created_at: 创建时间, updated_at: 更新时间) :datetime         not null
#  user_id(聊天室创建者ID)                                :bigint           not null
#
# Indexes
#
#  index_chat_rooms_on_created_at  (created_at)
#  index_chat_rooms_on_status      (status)
#  index_chat_rooms_on_user_id     (user_id)
#
# Foreign Keys
#
#  fk_rails_...  (user_id => users.id)
#

class ChatRoom < ApplicationRecord
  # 关联关系
  belongs_to :user
  has_many :chat_messages, dependent: :destroy
  has_many :chat_room_participants, dependent: :destroy
  has_many :participants, through: :chat_room_participants, source: :user
  has_many :ai_chat_sessions, dependent: :destroy

  # 枚举
  enum :status, { active: 0, inactive: 1 }

  # 验证
  validates :name, presence: true, length: { maximum: 100 }
  validates :description, length: { maximum: 500 }

  # 作用域
  scope :active, -> { where(status: :active) }
  scope :for_user, ->(user) { joins(:chat_room_participants).where(chat_room_participants: { user: user }) }

  # 回调
  after_create :add_creator_as_participant

  # 实例方法
  def add_participant(user)
    chat_room_participants.find_or_create_by(user: user)
  end

  def remove_participant(user)
    chat_room_participants.find_by(user: user)&.destroy
  end

  def participant?(user)
    chat_room_participants.exists?(user: user)
  end

  def total_token_usage
    chat_messages.sum(:token_count)
  end

  def last_message
    chat_messages.order(:created_at).last
  end

  private

  def add_creator_as_participant
    add_participant(user)
  end
end
