# == Schema Information
#
# Table name: chat_room_participants(聊天室参与者表 - 管理用户与聊天室的关系)
#
#  id                                                     :bigint           not null, primary key
#  joined_at(加入聊天室的时间)                            :datetime         not null
#  left_at(离开聊天室的时间，null表示仍在聊天室中)        :datetime
#  role(参与者角色：0-普通成员，1-管理员)                 :integer          default("member"), not null
#  created_at(created_at: 创建时间, updated_at: 更新时间) :datetime         not null
#  updated_at(created_at: 创建时间, updated_at: 更新时间) :datetime         not null
#  chat_room_id(关联聊天室ID)                             :bigint           not null
#  user_id(关联用户ID)                                    :bigint           not null
#
# Indexes
#
#  index_chat_room_participants_on_chat_room_id              (chat_room_id)
#  index_chat_room_participants_on_chat_room_id_and_user_id  (chat_room_id,user_id) UNIQUE
#  index_chat_room_participants_on_joined_at                 (joined_at)
#  index_chat_room_participants_on_left_at                   (left_at)
#  index_chat_room_participants_on_role                      (role)
#  index_chat_room_participants_on_user_id                   (user_id)
#
# Foreign Keys
#
#  fk_rails_...  (chat_room_id => chat_rooms.id)
#  fk_rails_...  (user_id => users.id)
#

class ChatRoomParticipant < ApplicationRecord
  # 关联关系
  belongs_to :chat_room
  belongs_to :user

  # 枚举
  enum :role, { member: 0, admin: 1 }

  # 验证
  validates :user_id, uniqueness: { scope: :chat_room_id }

  # 作用域
  scope :active, -> { where(left_at: nil) }
  scope :admins, -> { where(role: :admin) }
  scope :members, -> { where(role: :member) }

  # 回调
  before_create :set_joined_at

  # 实例方法
  def active?
    left_at.nil?
  end

  def leave!
    update!(left_at: Time.current)
  end

  def rejoin!
    update!(left_at: nil, joined_at: Time.current)
  end

  private

  def set_joined_at
    self.joined_at ||= Time.current
  end
end
