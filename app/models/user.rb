# == Schema Information
#
# Table name: users(用户表 - 存储用户基本信息和认证数据)
#
#  id                                                                                              :bigint           not null, primary key
#  birthday(生日)                                                                                  :date
#  email(邮箱地址，用于登录和验证)                                                                 :string(255)      not null
#  email_verified(邮箱是否已验证)                                                                  :boolean          default(FALSE), not null
#  email_verified_at(邮箱验证时间)                                                                 :datetime
#  exercise_frequency(运动频率：0-偶尔锻炼(0-2次/周)，1-定期锻炼(3-5次/周)，2-专业运动员(6+次/周)) :integer
#  fitness_goals(用户健身目标(JSON数组))                                                           :text
#  gender(性别：0-未知，1-男，2-女)                                                                :integer          default("unknown")
#  height(身高(cm))                                                                                :decimal(5, 2)
#  last_login_at(最后登录时间)                                                                     :datetime
#  last_login_ip(最后登录IP地址)                                                                   :string
#  name(用户姓名)                                                                                  :string(100)      not null
#  obstacles(阻碍用户实现目标的因素(JSON数组))                                                     :text
#  password_digest(加密后的密码)                                                                   :string           not null
#  password_reset_sent_at(密码重置令牌发送时间)                                                    :datetime
#  password_reset_token(密码重置令牌)                                                              :string
#  roles(用户角色数组：支持多个角色如['user', 'admin'])                                            :string           default(["user"]), not null, is an Array
#  status(用户状态：0-待验证，1-已激活，2-已禁用)                                                  :integer          default("inactive"), not null
#  weight(体重(kg))                                                                                :decimal(5, 2)
#  created_at(created_at: 创建时间, updated_at: 更新时间)                                          :datetime         not null
#  updated_at(created_at: 创建时间, updated_at: 更新时间)                                          :datetime         not null
#
# Indexes
#
#  index_users_on_birthday              (birthday)
#  index_users_on_email                 (email) UNIQUE
#  index_users_on_email_verified        (email_verified)
#  index_users_on_exercise_frequency    (exercise_frequency)
#  index_users_on_gender                (gender)
#  index_users_on_last_login_at         (last_login_at)
#  index_users_on_password_reset_token  (password_reset_token) UNIQUE
#  index_users_on_roles                 (roles) USING gin
#  index_users_on_roles_and_status      (roles,status)
#
class User < ApplicationRecord
  has_secure_password

  # 关联关系
  has_many :sessions, dependent: :destroy
  has_many :chat_rooms, dependent: :destroy
  has_many :chat_room_participants, dependent: :destroy
  has_many :participating_chat_rooms, through: :chat_room_participants, source: :chat_room
  has_many :chat_messages, dependent: :destroy
  has_many :ai_chat_sessions, dependent: :destroy
  has_many :exercise_analyses, dependent: :destroy

  # 数据验证
  validates :email, presence: true,
            uniqueness: { case_sensitive: false },
            format: { with: URI::MailTo::EMAIL_REGEXP }
  validates :name, presence: true
  validates :password, length: { minimum: 6 }, if: :password_required?
  validates :roles, presence: true
  validate :roles_are_valid

  # 可用角色常量
  AVAILABLE_ROLES = %w[user admin].freeze

  # 用户状态枚举
  enum :status, {
    active: 0, # 正常
    inactive: 1, # 非活跃
    suspended: 2 # 被暂停
  }

  # 性别枚举
  enum :gender, {
    unknown: 0, # 未知
    male: 1, # 男
    female: 2 # 女
  }

  # 运动频率枚举
  enum :exercise_frequency, {
    occasionally: 0, # 偶尔锻炼(0-2次/周)
    regular: 1, # 定期锻炼(3-5次/周)
    athlete: 2 # 专业运动员(6+次/周)
  }

  # JSON字段访问器
  serialize :fitness_goals, coder: JSON
  serialize :obstacles, coder: JSON

  # 数据验证
  validates :weight, numericality: { greater_than: 0, less_than: 1000 }, allow_blank: true
  validates :height, numericality: { greater_than: 0, less_than: 300 }, allow_blank: true
  validates :birthday, presence: false
  validate :birthday_not_in_future

  # 回调
  before_save :downcase_email
  before_validation :ensure_default_roles, on: :create
  before_validation :set_name_from_email, if: -> { name.blank? }

  # 检查邮箱是否已验证
  def email_verified?
    email_verified
  end

  # 标记邮箱为已验证
  def verify_email!
    update!(email_verified: true, email_verified_at: Time.current)
  end

  # 生成重置密码令牌
  def generate_reset_password_token!
    self.reset_password_token = SecureRandom.urlsafe_base64(32)
    self.reset_password_sent_at = Time.current
    save!
  end

  # 清除重置密码令牌
  def clear_reset_password_token!
    update!(reset_password_token: nil, reset_password_sent_at: nil)
  end

  # 检查重置密码令牌是否有效
  def reset_password_token_valid?
    reset_password_token.present? &&
      reset_password_sent_at.present? &&
      reset_password_sent_at > 2.hours.ago
  end

  # 更新最后登录信息
  def update_last_login!(ip_address)
    update!(
      last_login_at: Time.current,
      last_login_ip: ip_address
    )
  end

  # 获取用户显示名
  def display_name
    name.present? ? name : email
  end

  # 检查是否为管理员
  def admin?
    roles.include?("admin")
  end

  # 检查是否为普通用户
  def user?
    roles.include?("user")
  end

  # 检查是否有指定角色
  def has_role?(role)
    roles.include?(role.to_s)
  end

  # 添加角色
  def add_role(role)
    return false unless AVAILABLE_ROLES.include?(role.to_s)
    return false if has_role?(role)

    self.roles = (roles + [role.to_s]).uniq
    save
  end

  # 移除角色
  def remove_role(role)
    return false unless has_role?(role)

    self.roles = roles - [role.to_s]
    # 确保至少有一个角色
    self.roles = ["user"] if roles.empty?
    save
  end

  # 设置角色（替换所有角色）
  def set_roles(new_roles)
    valid_roles = Array.wrap(new_roles).map(&:to_s) & AVAILABLE_ROLES
    valid_roles = ["user"] if valid_roles.empty?

    self.roles = valid_roles.uniq
    save
  end

  # 获取角色显示名称
  def roles_text
    roles.map { |role| I18n.t("user.roles.#{role}") }.join(", ")
  end

  # 获取最高权限角色
  def primary_role
    return "admin" if admin?
    "user"
  end

  # 计算年龄
  def age
    return nil unless birthday.present?

    today = Date.current
    age = today.year - birthday.year
    age -= 1 if today < birthday + age.years
    age
  end

  # 计算BMI
  def bmi
    return nil unless weight.present? && height.present? && height > 0

    height_m = height / 100.0 # 转换为米
    (weight / (height_m * height_m)).round(2)
  end

  # BMI状态
  def bmi_status
    return I18n.t("user.bmi_status.unknown") unless bmi.present?

    case bmi
    when 0..18.4
      I18n.t("user.bmi_status.underweight")
    when 18.5..23.9
      I18n.t("user.bmi_status.normal")
    when 24.0..27.9
      I18n.t("user.bmi_status.overweight")
    else
      I18n.t("user.bmi_status.obese")
    end
  end

  # 性别中文
  def gender_text
    case gender
    when "male"
      I18n.t("user.gender.male")
    when "female"
      I18n.t("user.gender.female")
    else
      I18n.t("user.gender.unknown")
    end
  end

  # 运动频率中文
  def exercise_frequency_text
    case exercise_frequency
    when "occasionally"
      I18n.t("user.exercise_frequency.occasionally")
    when "regular"
      I18n.t("user.exercise_frequency.regular")
    when "athlete"
      I18n.t("user.exercise_frequency.athlete")
    else
      I18n.t("user.exercise_frequency.unknown")
    end
  end

  # 获取健身目标数组
  def fitness_goals_array
    return [] unless fitness_goals.present?
    fitness_goals.is_a?(Array) ? fitness_goals : []
  end

  # 获取阻碍因素数组
  def obstacles_array
    return [] unless obstacles.present?
    obstacles.is_a?(Array) ? obstacles : []
  end

  # 检查问卷是否完成
  def questionnaire_completed?
    exercise_frequency.present? &&
      fitness_goals_array.any? &&
      obstacles_array.any?
  end

  # 作用域
  scope :verified, -> { where.not(email_verified_at: nil) }
  scope :unverified, -> { where(email_verified_at: nil) }
  scope :with_role, ->(role) { where("roles && ARRAY[?]::varchar[]", role) }
  scope :admins, -> { with_role("admin") }
  scope :users, -> { with_role("user") }
  scope :by_gender, ->(gender) { where(gender: gender) if gender.present? }
  scope :by_exercise_frequency, ->(frequency) { where(exercise_frequency: frequency) if frequency.present? }
  scope :questionnaire_completed, -> { where.not(exercise_frequency: nil).where.not(fitness_goals: [nil, ""]).where.not(obstacles: [nil, ""]) }
  scope :questionnaire_incomplete, -> { where(exercise_frequency: nil).or(where(fitness_goals: [nil, ""])).or(where(obstacles: [nil, ""])) }
  scope :adults, -> { where("birthday < ?", 18.years.ago) }

  private

  # 将邮箱转为小写
  def downcase_email
    self.email = email.downcase if email.present?
  end

  # 检查是否需要密码验证
  def password_required?
    # 新记录时，如果没有提供密码，则不要求密码（支持验证码注册）
    # 如果有提供密码，则进行密码验证
    new_record? ? password.present? : password.present?
  end

  # 生日不能是未来日期
  def birthday_not_in_future
    return unless birthday.present?

    if birthday > Date.current
      errors.add(:birthday, I18n.t("user.validations.birthday_future"))
    end
  end

  # 验证角色有效性
  def roles_are_valid
    return unless roles.present?

    invalid_roles = roles - AVAILABLE_ROLES
    if invalid_roles.any?
      errors.add(:roles, I18n.t("user.validations.invalid_roles", roles: invalid_roles.join(", ")))
    end
  end

  # 确保新用户默认有user角色
  def ensure_default_roles
    self.roles = ["user"] if roles.blank?
  end

  def set_name_from_email
    self.name = email.split("@").first
  end
end
