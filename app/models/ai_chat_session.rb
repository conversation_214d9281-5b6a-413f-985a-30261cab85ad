# == Schema Information
#
# Table name: ai_chat_sessions(AI聊天会话表 - 管理AI聊天的会话信息和统计数据)
#
#  id                                                     :bigint           not null, primary key
#  last_message_at(最后一条消息的时间)                    :datetime
#  model_config(AI模型配置，包括温度、最大token等参数)    :json
#  session_name(会话名称，用户自定义)                     :string(100)
#  status(会话状态：0-活跃，1-已归档，2-已删除)           :integer          default("active"), not null
#  total_completion_tokens(会话总输出token数量)           :integer          default(0)
#  total_prompt_tokens(会话总输入token数量)               :integer          default(0)
#  total_tokens(会话总消耗token数量)                      :integer          default(0)
#  created_at(created_at: 创建时间, updated_at: 更新时间) :datetime         not null
#  updated_at(created_at: 创建时间, updated_at: 更新时间) :datetime         not null
#  chat_room_id(关联聊天室ID)                             :bigint           not null
#  user_id(关联用户ID)                                    :bigint           not null
#
# Indexes
#
#  index_ai_chat_sessions_on_chat_room_id             (chat_room_id)
#  index_ai_chat_sessions_on_chat_room_id_and_status  (chat_room_id,status)
#  index_ai_chat_sessions_on_created_at               (created_at)
#  index_ai_chat_sessions_on_last_message_at          (last_message_at)
#  index_ai_chat_sessions_on_status                   (status)
#  index_ai_chat_sessions_on_user_id                  (user_id)
#  index_ai_chat_sessions_on_user_id_and_status       (user_id,status)
#
# Foreign Keys
#
#  fk_rails_...  (chat_room_id => chat_rooms.id)
#  fk_rails_...  (user_id => users.id)
#

class AiChatSession < ApplicationRecord
  # 关联关系
  belongs_to :user
  belongs_to :chat_room

  # 枚举
  enum :status, { active: 0, archived: 1, deleted: 2 }

  # 验证
  validates :session_name, length: { maximum: 100 }

  # 作用域
  scope :recent, -> { order(last_message_at: :desc, created_at: :desc) }
  scope :active, -> { where(status: :active) }

  # 回调
  before_create :set_default_session_name
  after_update :update_totals, if: :saved_change_to_total_tokens?

  # 实例方法
  def update_token_usage!(prompt_tokens: 0, completion_tokens: 0)
    increment!(:total_prompt_tokens, prompt_tokens)
    increment!(:total_completion_tokens, completion_tokens)
    increment!(:total_tokens, prompt_tokens + completion_tokens)
    touch(:last_message_at)
  end

  def messages
    chat_room.chat_messages.order(:created_at)
  end

  def ai_messages
    messages.ai
  end

  def user_messages
    messages.user
  end

  def archive!
    update!(status: :archived)
  end

  def soft_delete!
    update!(status: :deleted)
  end

  def cost_estimate(model_pricing = {})
    # Moonshot模型的定价（按实际API定价调整）
    default_pricing = { prompt_price_per_1k: 0.012, completion_price_per_1k: 0.012 }
    pricing = model_pricing.present? ? model_pricing : default_pricing
    
    prompt_cost = (total_prompt_tokens.to_f / 1000) * pricing[:prompt_price_per_1k]
    completion_cost = (total_completion_tokens.to_f / 1000) * pricing[:completion_price_per_1k]
    prompt_cost + completion_cost
  end

  private

  def set_default_session_name
    self.session_name ||= "Chat Session #{Time.current.strftime('%Y-%m-%d %H:%M')}"
  end

  def update_totals
    # 这里可以添加token使用统计逻辑
    Rails.logger.info "Session #{id} token usage updated: #{total_tokens} total tokens"
  end
end
