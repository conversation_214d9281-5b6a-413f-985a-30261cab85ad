# == Schema Information
#
# Table name: sessions(会话表 - 管理用户登录会话和API认证)
#
#  id                                                     :bigint           not null, primary key
#  expires_at(会话过期时间)                               :datetime         not null
#  ip_address(登录IP地址)                                 :string
#  revoked(是否已撤销)                                    :boolean          default(FALSE), not null
#  revoked_at(撤销时间)                                   :datetime
#  token(会话令牌，用于API认证)                           :string(255)      not null
#  user_agent(用户代理信息)                               :string
#  created_at(created_at: 创建时间, updated_at: 更新时间) :datetime         not null
#  updated_at(created_at: 创建时间, updated_at: 更新时间) :datetime         not null
#  user_id(关联用户ID)                                    :bigint           not null
#
# Indexes
#
#  index_sessions_on_expires_at              (expires_at)
#  index_sessions_on_revoked_and_expires_at  (revoked,expires_at)
#  index_sessions_on_token                   (token) UNIQUE
#  index_sessions_on_user_id                 (user_id)
#  index_sessions_on_user_id_and_revoked     (user_id,revoked)
#
# Foreign Keys
#
#  fk_rails_...  (user_id => users.id)
#
class Session < ApplicationRecord
  # 关联关系
  belongs_to :user

  # 数据验证
  validates :token, presence: true, uniqueness: true
  validates :expires_at, presence: true

  # 回调
  before_validation :generate_token, on: :create

  # 作用域
  scope :active, -> { where(revoked_at: nil).where('expires_at > ?', Time.current) }
  scope :expired, -> { where('expires_at <= ?', Time.current) }
  scope :revoked, -> { where.not(revoked_at: nil) }

  # 为用户创建新会话
  def self.create_for_user!(user, ip_address: nil, user_agent: nil)
    create!(
      user: user,
      ip_address: ip_address,
      user_agent: user_agent,
      expires_at: 30.days.from_now
    )
  end

  # 通过令牌查找活跃会话
  def self.find_by_token(token)
    active.find_by(token: token)
  end

  # 检查会话是否活跃
  def active?
    revoked_at.nil? && expires_at > Time.current
  end

  # 检查会话是否过期
  def expired?
    expires_at <= Time.current
  end

  # 检查会话是否已撤销
  def revoked?
    revoked_at.present?
  end

  # 撤销会话
  def revoke!
    update!(revoked_at: Time.current)
  end

  # 延长会话时间
  def extend_session!(duration = 30.days)
    update!(expires_at: duration.from_now)
  end

  # 清理过期会话
  def self.cleanup_expired!
    expired.delete_all
  end

  # 撤销用户的所有其他会话
  def revoke_other_sessions!
    user.sessions.active.where.not(id: id).update_all(revoked_at: Time.current)
  end

  private

  # 生成会话令牌
  def generate_token
    self.token = SecureRandom.urlsafe_base64(32) if token.blank?
  end
end
