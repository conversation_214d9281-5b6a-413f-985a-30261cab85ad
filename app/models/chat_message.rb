# == Schema Information
#
# Table name: chat_messages(聊天消息表 - 存储聊天室中的用户和AI消息)
#
#  id                                                       :bigint           not null, primary key
#  ai_model_name(AI模型名称，如GPT-4、<PERSON>等)             :string
#  completion_tokens(AI回复消耗的token数量)                 :integer          default(0)
#  content(消息内容)                                        :text             not null
#  message_type(消息类型：0-用户消息，1-AI消息，2-系统消息) :integer          default("user"), not null
#  metadata(额外的元数据，如模型参数、配置等)               :json
#  prompt_tokens(输入prompt消耗的token数量)                 :integer          default(0)
#  status(消息状态：0-已发送，1-处理中，2-失败)             :integer          default("sent"), not null
#  token_count(消耗的token总数量)                           :integer          default(0)
#  created_at(created_at: 创建时间, updated_at: 更新时间)   :datetime         not null
#  updated_at(created_at: 创建时间, updated_at: 更新时间)   :datetime         not null
#  chat_room_id(关联聊天室ID)                               :bigint           not null
#  parent_message_id(父消息ID，用于回复和引用关系)          :bigint
#  user_id(用户ID，AI消息时为null)                          :bigint
#
# Indexes
#
#  index_chat_messages_on_chat_room_id                 (chat_room_id)
#  index_chat_messages_on_chat_room_id_and_created_at  (chat_room_id,created_at)
#  index_chat_messages_on_created_at                   (created_at)
#  index_chat_messages_on_message_type                 (message_type)
#  index_chat_messages_on_parent_message_id            (parent_message_id)
#  index_chat_messages_on_status                       (status)
#  index_chat_messages_on_user_id                      (user_id)
#
# Foreign Keys
#
#  fk_rails_...  (chat_room_id => chat_rooms.id)
#  fk_rails_...  (parent_message_id => chat_messages.id)
#  fk_rails_...  (user_id => users.id)
#

class ChatMessage < ApplicationRecord
  # 关联关系
  belongs_to :chat_room
  belongs_to :user, optional: true # AI消息时为空
  belongs_to :parent_message, class_name: 'ChatMessage', optional: true
  has_many :replies, class_name: 'ChatMessage', foreign_key: 'parent_message_id', dependent: :destroy

  # 枚举
  enum :message_type, { user: 0, ai: 1, system: 2 }
  enum :status, { sent: 0, processing: 1, failed: 2 }

  # 验证
  validates :content, presence: true
  validates :token_count, :prompt_tokens, :completion_tokens, numericality: { greater_than_or_equal_to: 0 }
  validates :user_id, presence: true, if: :user_message?
  validates :ai_model_name, presence: true, if: :ai_message?

  # 作用域
  scope :recent, -> { order(created_at: :desc) }
  scope :successful, -> { where(status: :sent) }
  scope :by_type, ->(type) { where(message_type: type) }
  scope :with_tokens, -> { where('token_count > 0') }

  # 回调
  before_save :calculate_total_tokens
  after_create_commit :broadcast_message

  # 实例方法
  def ai_message?
    message_type == 'ai'
  end

  def user_message?
    message_type == 'user'
  end

  def system_message?
    message_type == 'system'
  end

  def has_replies?
    replies.exists?
  end

  def reply_to(content:, user: nil, message_type: :ai, **options)
    replies.create!(
      content: content,
      user: user,
      message_type: message_type,
      chat_room: chat_room,
      **options
    )
  end

  # 计算消息的大概token数量（如果没有提供准确的token数据）
  def estimate_tokens
    OpenAI.rough_token_count(content)
  end

  private

  def calculate_total_tokens
    self.token_count = prompt_tokens + completion_tokens if token_count.zero?
  end

  def broadcast_message
    # 通过 Action Cable 广播消息
    ActionCable.server.broadcast(
      "chat_room_#{chat_room_id}",
      {
        type: 'new_message',
        message: {
          id: id,
          content: content,
          message_type: message_type,
          user: user&.slice(:id, :name, :email),
          token_count: token_count,
          created_at: created_at.iso8601,
          parent_message_id: parent_message_id
        }
      }
    )
  end
end
