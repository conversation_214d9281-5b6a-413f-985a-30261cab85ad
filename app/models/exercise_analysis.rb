# == Schema Information
#
# Table name: exercise_analyses(运动分析记录表 - 存储用户上传的运动数据图片和分析结果)
#
#  id                                                                                          :bigint           not null, primary key
#  analysis_result(分析结果(JSON格式))                                                         :json
#  status(分析状态：0-pending(待处理), 1-analyzing(分析中), 2-completed(完成), 3-failed(失败)) :integer          default("pending"), not null
#  created_at(created_at: 创建时间, updated_at: 更新时间)                                      :datetime         not null
#  updated_at(created_at: 创建时间, updated_at: 更新时间)                                      :datetime         not null
#  user_id(关联用户ID)                                                                         :bigint           not null
#
# Indexes
#
#  index_exercise_analyses_on_created_at  (created_at)
#  index_exercise_analyses_on_status      (status)
#  index_exercise_analyses_on_user_id     (user_id)
#
# Foreign Keys
#
#  fk_rails_...  (user_id => users.id)
#

class ExerciseAnalysis < ApplicationRecord
  belongs_to :user
  has_many_attached :images, service: :amazon

  # 分析状态枚举
  enum :status, {
    pending: 0, # 待分析
    analyzing: 1, # 分析中
    completed: 2, # 分析完成
    failed: 3 # 分析失败
  }

  # 验证
  validates :user_id, presence: true

  # 作用域
  scope :recent, -> { order(created_at: :desc) }
  scope :by_user, ->(user) { where(user: user) }
  scope :with_status, ->(status) { where(status: status) }

  # 回调
  after_commit :trigger_analysis, on: :create

  # 触发AI分析
  def trigger_analysis
    # 异步执行分析
    ExerciseAnalysisJob.perform_later(id)
  end

  # 更新分析结果
  def update_analysis_result(result)
    update!(
      analysis_result: result,
      status: :completed
    )
  end

  # 标记分析失败
  def mark_as_failed(error_message = nil)
    update!(
      status: :failed,
      analysis_result: { error: error_message }
    )
  end

  # 获取分析结果的各个部分
  def overall_performance
    analysis_result&.dig('analysis', 'overallPerformance') || {}
  end

  def pace_and_heart_rate_analysis
    analysis_result&.dig('analysis', 'paceAnalysis') || {}
  end

  def heart_rate_zone_distribution
    analysis_result&.dig('analysis', 'heartRateZones') || {}
  end

  def calorie_consumption_analysis
    analysis_result&.dig('analysis', 'calorieMetabolism') || {}
  end

  def cadence_and_running_efficiency
    analysis_result&.dig('analysis', 'runningEfficiency') || {}
  end

  def environmental_factor_impact
    analysis_result&.dig('analysis', 'environmentalFactors') || {}
  end

  def exercise_effect_and_physical_improvement
    analysis_result&.dig('analysis', 'fitnessImprovement') || {}
  end

  def training_quality_evaluation_and_suggestions
    analysis_result&.dig('analysis', 'trainingQuality') || {}
  end

  def weekly_plan_arrangement
    analysis_result&.dig('analysis', 'weeklyPlan') || {}
  end

  def nutrition_suggestions
    analysis_result&.dig('analysis', 'nutritionAdvice') || {}
  end

  def planned_vs_actual_execution
    analysis_result&.dig('analysis', 'planVsActual') || {}
  end

  def exercise_performance_analysis
    analysis_result&.dig('analysis', 'performanceAnalysis') || {}
  end

  # 获取图片URL
  def image_urls
    images.map do |image|
      # if Rails.application.config.active_storage.service == :amazon
      image.url
      # else
      #   Rails.application.routes.url_helpers.rails_blob_url(image, only_path: true)
      # end
    end
  end
end
