# == Schema Information
#
# Table name: verification_codes(验证码表 - 管理邮箱验证和密码重置验证码)
#
#  id                                                     :bigint           not null, primary key
#  attempts(尝试次数)                                     :integer          default(0), not null
#  code(验证码)                                           :string(10)       not null
#  code_type(验证码类型：0-邮箱验证，1-密码重置)          :integer          not null
#  email(验证码发送的邮箱地址)                            :string(255)      not null
#  expires_at(验证码过期时间)                             :datetime         not null
#  used(是否已使用)                                       :boolean          default(FALSE), not null
#  used_at(使用时间)                                      :datetime
#  created_at(created_at: 创建时间, updated_at: 更新时间) :datetime         not null
#  updated_at(created_at: 创建时间, updated_at: 更新时间) :datetime         not null
#
# Indexes
#
#  index_verification_codes_on_code_and_code_type            (code,code_type)
#  index_verification_codes_on_email_and_code_type_and_used  (email,code_type,used)
#  index_verification_codes_on_email_and_expires_at          (email,expires_at)
#  index_verification_codes_on_expires_at                    (expires_at)
#
class VerificationCode < ApplicationRecord
  # 数据验证
  validates :email, presence: true, format: { with: URI::MailTo::EMAIL_REGEXP }
  validates :code, presence: true, length: { is: 6 }
  validates :code_type, presence: true
  validates :expires_at, presence: true

  # 验证码类型枚举
  enum :code_type, {
    email_verification: 0,    # 邮箱验证
    password_reset: 1         # 密码重置
  }

  # 作用域
  scope :valid_codes, -> { where('expires_at > ? AND used = ?', Time.current, false) }
  scope :for_email, ->(email) { where(email: email) }
  scope :for_code_type, ->(code_type) { where(code_type: code_type) }

  # 生成6位数字验证码
  def self.generate_code
    return '555555' if Rails.env.development?

    SecureRandom.random_number(900000) + 100000
  end

  # 为邮箱创建新的验证码
  def self.create_for_email(email, code_type = 'email_verification')
    # 先使现有的验证码失效
    where(email: email, code_type: code_type, used: false).update_all(used: true, used_at: Time.current)
    
    create!(
      email: email,
      code: generate_code.to_s,
      code_type: code_type,
      expires_at: 10.minutes.from_now,
      attempts: 0
    )
  end

  # 验证验证码
  def self.verify_code(email, code, code_type = 'email_verification')
    verification_code = valid_codes
                       .for_email(email)
                       .for_code_type(code_type)
                       .find_by(code: code)
    
    if verification_code
      verification_code.update!(used: true, used_at: Time.current)
      true
    else
      false
    end
  end

  # 增加尝试次数
  def increment_attempts!
    increment!(:attempts)
  end

  # 检查是否已过期
  def expired?
    expires_at < Time.current
  end

  # 检查是否已使用
  def used?
    used
  end

  # 检查是否有效
  def valid_code?
    !expired? && !used?
  end

  # 检查尝试次数是否超限
  def attempts_exceeded?
    attempts >= 5
  end
end
