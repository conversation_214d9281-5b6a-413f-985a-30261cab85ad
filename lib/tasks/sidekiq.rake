namespace :sidekiq do
  desc "Start Sidekiq"
  task start: :environment do
    puts "启动 Sidekiq..."
    pidfile = Rails.root.join('tmp', 'pids', 'sidekiq.pid')
    logfile = Rails.root.join('log', 'sidekiq.log')
    
    # 启动 Sidekiq 并记录 PID
    pid = spawn("bundle exec sidekiq -C config/sidekiq.yml", 
                out: logfile.to_s, 
                err: logfile.to_s)
    
    File.write(pidfile, pid)
    Process.detach(pid)
    
    puts "Sidekiq 已启动，PID: #{pid}"
    puts "日志文件: #{logfile}"
  end

  desc "Stop Sidekiq"
  task stop: :environment do
    pidfile = Rails.root.join('tmp', 'pids', 'sidekiq.pid')
    if File.exist?(pidfile)
      pid = File.read(pidfile).to_i
      puts "停止 Sidekiq (PID: #{pid})..."
      Process.kill('TERM', pid)
      File.delete(pidfile)
      puts "Sidekiq 已停止"
    else
      puts "Sidekiq 未运行"
    end
  end

  desc "Restart Sidekiq"
  task restart: :environment do
    Rake::Task['sidekiq:stop'].invoke
    sleep 2
    Rake::Task['sidekiq:start'].invoke
  end

  desc "Show Sidekiq status"
  task status: :environment do
    pidfile = Rails.root.join('tmp', 'pids', 'sidekiq.pid')
    if File.exist?(pidfile)
      pid = File.read(pidfile).to_i
      begin
        Process.kill(0, pid)
        puts "Sidekiq 正在运行 (PID: #{pid})"
        puts "Web UI: http://localhost:3000/sidekiq"
      rescue Errno::ESRCH
        puts "Sidekiq PID 文件存在但进程未运行"
        File.delete(pidfile)
      end
    else
      puts "Sidekiq 未运行"
    end
  end

  namespace :cron do
    desc "Load cron jobs from config/sidekiq.yml"
    task load: :environment do
      require 'sidekiq-cron'
      
      schedule_file = Rails.root.join('config', 'sidekiq.yml')
      if File.exist?(schedule_file)
        schedule = YAML.load_file(schedule_file)
        if schedule && schedule[:cron]
          schedule[:cron].each do |name, job_config|
            job = Sidekiq::Cron::Job.create(
              name: name.to_s,
              cron: job_config['cron'],
              class: job_config['class'],
              queue: job_config['queue'] || 'default'
            )
            
            if job
              puts "✅ 定时任务已加载: #{name}"
            else
              puts "❌ 定时任务加载失败: #{name}"
            end
          end
          puts "定时任务加载完成"
        else
          puts "配置文件中未找到定时任务"
        end
      else
        puts "配置文件不存在: #{schedule_file}"
      end
    end

    desc "List all cron jobs"
    task list: :environment do
      require 'sidekiq-cron'
      
      jobs = Sidekiq::Cron::Job.all
      if jobs.any?
        puts "定时任务列表:"
        puts "-" * 80
        jobs.each do |job|
          status = job.enabled? ? "启用" : "禁用"
          puts "名称: #{job.name}"
          puts "状态: #{status}"
          puts "Cron: #{job.cron}"
          puts "类名: #{job.klass}"
          puts "上次运行: #{job.last_enqueue_time || '从未运行'}"
          puts "-" * 80
        end
      else
        puts "没有定时任务"
      end
    end

    desc "Remove all cron jobs"
    task clear: :environment do
      require 'sidekiq-cron'
      
      Sidekiq::Cron::Job.destroy_all!
      puts "所有定时任务已清除"
    end
  end
end