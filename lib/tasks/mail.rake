namespace :mail do
  desc "测试邮件发送配置"
  task test: :environment do
    puts "🧪 开始测试邮件发送配置..."
    puts "当前环境: #{Rails.env}"
    puts "邮件发送方式: #{ActionMailer::Base.delivery_method}"
    puts "-" * 50
    
    # 测试发送验证码邮件
    begin
      test_email = "<EMAIL>"
      test_code = "123456"
      test_purpose = "email_verification"
      
      puts "📧 发送测试邮件..."
      puts "收件人: #{test_email}"
      puts "验证码: #{test_code}"
      puts "用途: #{test_purpose}"
      puts "语言: 中文"
      puts "-" * 30
      
      # 发送邮件
      mail = VerificationCodeMailer.send_verification_code(
        test_email, 
        test_code, 
        test_purpose,
        :'zh-CN'
      )
      
      # 立即发送 (不经过 Sidekiq)
      mail.deliver_now
      
      puts "✅ 邮件发送成功!"
      
      # 如果是开发或测试环境，提示查看日志
      if Rails.env.development? || Rails.env.test?
        puts "💡 在开发/测试环境中，邮件内容已记录到日志中，请查看上方日志输出"
        puts "💡 您可以访问 http://localhost:3000/rails/mailers 预览邮件样式"
      else
        puts "💡 在生产环境中，邮件已真实发送到 #{test_email}"
      end
      
    rescue => e
      puts "❌ 邮件发送失败: #{e.message}"
      puts "错误详情: #{e.backtrace.first(5).join('\n')}"
    end
    
    puts "-" * 50
    puts "🎯 测试完成!"
  end
  
  desc "显示当前邮件配置"
  task config: :environment do
    puts "📋 当前邮件配置"
    puts "=" * 50
    puts "环境: #{Rails.env}"
    puts "发送方式: #{ActionMailer::Base.delivery_method}"
    puts "默认发件人: #{ActionMailer::Base.default[:from]}"
    
    case ActionMailer::Base.delivery_method
    when :smtp
      puts "SMTP 配置:"
      settings = ActionMailer::Base.smtp_settings
      puts "  地址: #{settings[:address]}"
      puts "  端口: #{settings[:port]}"
      puts "  域名: #{settings[:domain]}"
      puts "  用户名: #{settings[:user_name]}"
      puts "  SSL: #{settings[:ssl]}"
      puts "  认证方式: #{settings[:authentication]}"
    when :logger
      puts "📝 使用日志记录方式 (开发环境)"
    when :test
      puts "🧪 使用测试方式 (测试环境)"
    end
    
    puts "=" * 50
  end
end