namespace :mail do
  desc "完整的邮件功能验证测试"
  task full_test: :environment do
    puts "🧪 开始完整的邮件功能验证测试"
    puts "=" * 60
    
    # 1. 检查配置
    puts "📋 1. 检查当前配置:"
    puts "   环境: #{Rails.env}"
    puts "   发送方式: #{ActionMailer::Base.delivery_method}"
    puts "   默认发件人: #{Settings.mailer_from}"
    puts ""
    
    # 2. 测试邮件发送
    puts "📧 2. 测试邮件发送功能:"
    begin
      mail = VerificationCodeMailer.send_verification_code(
        '<EMAIL>',
        '888888',
        'email_verification',
        :'zh-CN'
      )
      
      puts "   ✅ 邮件对象创建成功"
      puts "   收件人: #{mail.to.first}"
      puts "   主题: #{mail.subject}"
      
      # 立即发送测试
      mail.deliver_now
      puts "   ✅ 邮件发送完成"
      
    rescue => e
      puts "   ❌ 邮件发送失败: #{e.message}"
    end
    
    puts ""
    
    # 3. 测试验证码服务
    puts "📝 3. 测试验证码服务:"
    begin
      result = VerificationCodeService.send_code(
        '<EMAIL>',
        'email_verification',
        :'zh-CN'
      )
      
      if result.success?
        puts "   ✅ 验证码服务调用成功"
        puts "   消息: #{result.message}"
        puts "   过期时间: #{result.data[:expires_at]}"
      else
        puts "   ❌ 验证码服务失败: #{result.message}"
      end
      
    rescue => e
      puts "   ❌ 验证码服务异常: #{e.message}"
    end
    
    puts ""
    
    # 4. 环境特定提示
    puts "💡 4. 环境特定说明:"
    case Rails.env
    when 'development'
      puts "   ✅ 开发环境 - 邮件内容已记录到日志和控制台"
      puts "   📋 访问 http://localhost:3000/rails/mailers 预览邮件样式"
    when 'test'
      puts "   ✅ 测试环境 - 邮件存储在内存中"
      puts "   📊 已发送邮件数量: #{ActionMailer::Base.deliveries.size}"
    when 'production'
      puts "   ✅ 生产环境 - 邮件已真实发送"
      puts "   📧 请检查收件箱确认邮件接收"
    end
    
    puts ""
    puts "=" * 60
    puts "🎯 测试完成! 邮件发送功能运行正常"
    puts "📚 详细文档: MAIL_DELIVERY_README.md"
  end
end