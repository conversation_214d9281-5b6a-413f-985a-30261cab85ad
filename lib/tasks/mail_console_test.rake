namespace :mail do
  desc "在控制台中测试邮件发送"
  task console_test: :environment do
    puts "🧪 开始控制台邮件测试..."
    puts "当前环境: #{Rails.env}"
    puts "邮件发送方式: #{ActionMailer::Base.delivery_method}"
    puts "=" * 50
    
    # 直接执行邮件发送测试
    mail = VerificationCodeMailer.send_verification_code(
      '<EMAIL>', 
      '888888', 
      'email_verification',
      :'zh-CN'
    )
    
    puts "📧 准备发送邮件..."
    puts "收件人: #{mail.to.first}"
    puts "主题: #{mail.subject}"
    puts "发送方式: #{ActionMailer::Base.delivery_method}"
    puts "-" * 30
    
    # 立即发送
    mail.deliver_now
    
    puts "✅ 邮件处理完成!"
    puts "💡 请检查上方的日志输出"
    puts "=" * 50
  end
end