# 自定义邮件发送方法
module MailDeliveryMethods
  # 日志记录邮件发送方法 - 用于开发和测试环境
  class LoggerDelivery
    def initialize(settings)
      @settings = settings
    end

    def deliver!(mail)
      # 使用标准输出和日志双重输出
      message = build_log_message(mail)
      
      # 输出到控制台
      puts message
      
      # 输出到 Rails 日志
      Rails.logger.info message
    end
    
    private
    
    def build_log_message(mail)
      lines = []
      lines << "=" * 80
      lines << "📧 邮件发送记录 (#{Rails.env}环境 - 仅记录日志，不实际发送)"
      lines << "=" * 80
      lines << "发送时间: #{Time.current.strftime('%Y-%m-%d %H:%M:%S %Z')}"
      lines << "收件人: #{mail.to.join(', ')}"
      lines << "发件人: #{mail.from.join(', ')}"
      lines << "主题: #{mail.subject}"
      lines << "内容类型: #{mail.content_type}"
      lines << "-" * 40
      
      if mail.multipart?
        lines << "邮件内容 (多部分):"
        mail.parts.each_with_index do |part, index|
          lines << "  第 #{index + 1} 部分 (#{part.content_type}):"
          
          body_content = extract_readable_content(part)
          
          if body_content.length > 300
            lines << "    #{body_content[0..300]}..."
            lines << "    (内容已截断，完整长度: #{body_content.length} 字符)"
          else
            lines << "    #{body_content}"
          end
          
          lines << "-" * 30 if index < mail.parts.size - 1
        end
      else
        lines << "邮件内容:"
        body_content = extract_readable_content(mail)
        
        if body_content.length > 500
          lines << "#{body_content[0..500]}..."
          lines << "(内容已截断，完整长度: #{body_content.length} 字符)"
        else
          lines << body_content
        end
      end
      
      lines << "=" * 80
      lines << "✅ 邮件已记录到日志 (未实际发送)"
      lines << "📋 提示: 访问 http://localhost:3000/rails/mailers 可预览邮件样式"
      lines << "=" * 80
      
      lines.join("\n")
    end
    
    def extract_readable_content(mail_part)
      body_content = mail_part.body.to_s
      
      return body_content if body_content.blank?
      
      # 处理编码问题
      begin
        # 获取 mail_part 的编码信息
        encoding = nil
        if mail_part.respond_to?(:encoding) && mail_part.encoding
          encoding = mail_part.encoding.downcase
        elsif mail_part.respond_to?(:content_transfer_encoding) && mail_part.content_transfer_encoding
          encoding = mail_part.content_transfer_encoding.downcase
        end
        
        # 如果是 quoted-printable 编码，进行解码
        if encoding&.include?('quoted-printable')
          body_content = Mail::Encodings::QuotedPrintable.decode(body_content)
        end
        
        # 如果是 base64 编码
        if encoding&.include?('base64')
          body_content = Mail::Encodings::Base64.decode(body_content)
        end
        
        # 处理字符编码
        if body_content.encoding != Encoding::UTF_8
          body_content = body_content.force_encoding('UTF-8')
        end
        
        # 清理 HTML 标签 (简单版本)
        if mail_part.content_type&.include?('text/html')
          body_content = body_content
            .gsub(/<[^>]+>/, '') # 移除 HTML 标签
            .gsub(/\s+/, ' ')    # 合并多个空白字符
            .strip
        end
        
      rescue => e
        body_content = "#{body_content[0..100]}... (解码失败: #{e.message})"
      end
      
      body_content
    end
  end
end

# 注册自定义发送方法
ActionMailer::Base.add_delivery_method :logger, MailDeliveryMethods::LoggerDelivery