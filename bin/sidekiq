#!/bin/bash

# Sidekiq 服务管理脚本
# 用法: ./bin/sidekiq-service [start|stop|restart|status]

RAILS_ROOT=$(cd "$(dirname "$0")/.." && pwd)
PIDFILE="$RAILS_ROOT/tmp/pids/sidekiq.pid"
LOGFILE="$RAILS_ROOT/log/sidekiq.log"

cd "$RAILS_ROOT" || exit 1

start_sidekiq() {
    if [ -f "$PIDFILE" ] && ps -p "$(cat "$PIDFILE")" > /dev/null 2>&1; then
        echo "Sidekiq 已经在运行，PID: $(cat "$PIDFILE")"
        return 1
    fi
    
    echo "启动 Sidekiq..."
    
    # 确保目录存在
    mkdir -p "$(dirname "$PIDFILE")"
    mkdir -p "$(dirname "$LOGFILE")"
    
    # 启动 Sidekiq
    nohup bundle exec sidekiq -C config/sidekiq.yml > "$LOGFILE" 2>&1 &
    local pid=$!
    
    echo $pid > "$PIDFILE"
    echo "Sidekiq 已启动，PID: $pid"
    echo "日志文件: $LOGFILE"
    echo "Web UI: http://localhost:3000/sidekiq"
}

stop_sidekiq() {
    if [ -f "$PIDFILE" ]; then
        local pid
        pid=$(cat "$PIDFILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            echo "停止 Sidekiq (PID: $pid)..."
            kill -TERM "$pid"
            
            # 等待进程结束
            local count=0
            while ps -p "$pid" > /dev/null 2>&1 && [ $count -lt 30 ]; do
                sleep 1
                count=$((count + 1))
            done
            
            if ps -p "$pid" > /dev/null 2>&1; then
                echo "强制停止 Sidekiq..."
                kill -KILL "$pid"
            fi
            
            rm -f "$PIDFILE"
            echo "Sidekiq 已停止"
        else
            echo "Sidekiq 进程不存在，清理 PID 文件"
            rm -f "$PIDFILE"
        fi
    else
        echo "Sidekiq 未运行"
    fi
}

status_sidekiq() {
    if [ -f "$PIDFILE" ]; then
        local pid
        pid=$(cat "$PIDFILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            echo "Sidekiq 正在运行，PID: $pid"
            echo "Web UI: http://localhost:3000/sidekiq"
            return 0
        else
            echo "Sidekiq PID 文件存在但进程未运行"
            rm -f "$PIDFILE"
            return 1
        fi
    else
        echo "Sidekiq 未运行"
        return 1
    fi
}

case "$1" in
    start)
        start_sidekiq
        ;;
    stop)
        stop_sidekiq
        ;;
    restart)
        stop_sidekiq
        sleep 2
        start_sidekiq
        ;;
    status)
        status_sidekiq
        ;;
    *)
        echo "用法: $0 {start|stop|restart|status}"
        echo ""
        echo "命令说明:"
        echo "  start   - 启动 Sidekiq"
        echo "  stop    - 停止 Sidekiq"
        echo "  restart - 重启 Sidekiq"
        echo "  status  - 查看 Sidekiq 状态"
        echo ""
        echo "Web UI: http://localhost:3000/sidekiq (开发环境)"
        exit 1
        ;;
esac