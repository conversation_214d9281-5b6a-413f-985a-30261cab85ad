#!/bin/bash

# Redis 服务管理脚本
# 用法: ./bin/redis [start|stop|restart|status|cli|config]

REDIS_CONFIG_FILE="/opt/homebrew/etc/redis.conf"
REDIS_PID_FILE="/opt/homebrew/var/run/redis.pid"
REDIS_LOG_FILE="/opt/homebrew/var/log/redis.log"
REDIS_PORT=6379

# 检测系统中的 Redis 安装路径
detect_redis_paths() {
    # 检查常见的安装路径
    if [ -f "/opt/homebrew/bin/redis-server" ]; then
        REDIS_SERVER="/opt/homebrew/bin/redis-server"
        REDIS_CLI="/opt/homebrew/bin/redis-cli"
        REDIS_CONFIG_FILE="/opt/homebrew/etc/redis.conf"
        REDIS_PID_FILE="/opt/homebrew/var/run/redis.pid"
        REDIS_LOG_FILE="/opt/homebrew/var/log/redis.log"
    elif [ -f "/usr/local/bin/redis-server" ]; then
        REDIS_SERVER="/usr/local/bin/redis-server"
        REDIS_CLI="/usr/local/bin/redis-cli"
        REDIS_CONFIG_FILE="/usr/local/etc/redis.conf"
        REDIS_PID_FILE="/usr/local/var/run/redis.pid"
        REDIS_LOG_FILE="/usr/local/var/log/redis.log"
    else
        # 使用系统 PATH 中的 redis
        REDIS_SERVER="redis-server"
        REDIS_CLI="redis-cli"
        REDIS_CONFIG_FILE="/etc/redis/redis.conf"
        REDIS_PID_FILE="/var/run/redis/redis.pid"
        REDIS_LOG_FILE="/var/log/redis/redis.log"
    fi
}

start_redis() {
    if redis_running; then
        echo "Redis 已经在运行，端口: $REDIS_PORT"
        return 1
    fi
    
    echo "启动 Redis..."
    
    # 确保目录存在
    mkdir -p "$(dirname "$REDIS_PID_FILE")" 2>/dev/null || true
    mkdir -p "$(dirname "$REDIS_LOG_FILE")" 2>/dev/null || true
    
    # 启动 Redis
    if [ -f "$REDIS_CONFIG_FILE" ]; then
        echo "使用配置文件: $REDIS_CONFIG_FILE"
        "$REDIS_SERVER" "$REDIS_CONFIG_FILE" --daemonize yes
    else
        echo "使用默认配置启动"
        "$REDIS_SERVER" --daemonize yes --port $REDIS_PORT
    fi
    
    # 等待启动
    sleep 2
    
    if redis_running; then
        echo "Redis 启动成功，端口: $REDIS_PORT"
        echo "日志文件: $REDIS_LOG_FILE"
    else
        echo "Redis 启动失败"
        return 1
    fi
}

stop_redis() {
    if ! redis_running; then
        echo "Redis 未运行"
        return 0
    fi
    
    echo "停止 Redis..."
    "$REDIS_CLI" -p $REDIS_PORT shutdown
    
    # 等待停止
    local count=0
    while redis_running && [ $count -lt 10 ]; do
        sleep 1
        count=$((count + 1))
    done
    
    if redis_running; then
        echo "强制停止 Redis..."
        if [ -f "$REDIS_PID_FILE" ]; then
            local pid
            pid=$(cat "$REDIS_PID_FILE")
            kill -KILL "$pid" 2>/dev/null || true
        fi
    fi
    
    echo "Redis 已停止"
}

redis_running() {
    "$REDIS_CLI" -p $REDIS_PORT ping >/dev/null 2>&1
}

status_redis() {
    if redis_running; then
        echo "Redis 正在运行，端口: $REDIS_PORT"
        echo "连接信息:"
        "$REDIS_CLI" -p $REDIS_PORT info server | grep -E "(redis_version|uptime_in_seconds|tcp_port)" | head -3
        return 0
    else
        echo "Redis 未运行"
        return 1
    fi
}

open_cli() {
    if ! redis_running; then
        echo "Redis 未运行，无法连接"
        return 1
    fi
    
    echo "连接到 Redis CLI (端口: $REDIS_PORT)..."
    echo "提示: 使用 'exit' 退出 CLI"
    "$REDIS_CLI" -p $REDIS_PORT
}

show_config() {
    echo "Redis 配置信息:"
    echo "服务器: $REDIS_SERVER"
    echo "CLI: $REDIS_CLI"
    echo "配置文件: $REDIS_CONFIG_FILE"
    echo "PID 文件: $REDIS_PID_FILE"
    echo "日志文件: $REDIS_LOG_FILE"
    echo "端口: $REDIS_PORT"
    
    if [ -f "$REDIS_CONFIG_FILE" ]; then
        echo ""
        echo "配置文件内容摘要:"
        grep -E "^(port|bind|save|maxmemory)" "$REDIS_CONFIG_FILE" 2>/dev/null || echo "无法读取配置文件"
    fi
}

# 检测 Redis 安装路径
detect_redis_paths

case "$1" in
    start)
        start_redis
        ;;
    stop)
        stop_redis
        ;;
    restart)
        stop_redis
        sleep 2
        start_redis
        ;;
    status)
        status_redis
        ;;
    cli)
        open_cli
        ;;
    config)
        show_config
        ;;
    *)
        echo "用法: $0 {start|stop|restart|status|cli|config}"
        echo ""
        echo "命令说明:"
        echo "  start   - 启动 Redis"
        echo "  stop    - 停止 Redis"
        echo "  restart - 重启 Redis"
        echo "  status  - 查看 Redis 状态"
        echo "  cli     - 打开 Redis CLI"
        echo "  config  - 显示配置信息"
        echo ""
        echo "示例:"
        echo "  $0 start     # 启动 Redis"
        echo "  $0 status    # 查看状态"
        echo "  $0 cli       # 进入 Redis 命令行"
        exit 1
        ;;
esac