#!/bin/bash

# DeepSport 服务管理脚本
# 用法: ./bin/services [start|stop|restart|status] [service]

RAILS_ROOT=$(cd "$(dirname "$0")/.." && pwd)

show_usage() {
    echo "用法: $0 [start|stop|restart|status] [service]"
    echo ""
    echo "服务列表:"
    echo "  redis    - Redis 数据库"
    echo "  sidekiq  - Sidekiq 后台任务"
    echo "  rails    - Rails 应用服务器"
    echo "  all      - 所有服务"
    echo ""
    echo "示例:"
    echo "  $0 start all      # 启动所有服务"
    echo "  $0 status         # 查看所有服务状态"
    echo "  $0 restart sidekiq # 重启 Sidekiq"
    echo "  $0 stop redis     # 停止 Redis"
}

start_service() {
    local service=$1
    case $service in
        redis)
            echo "🚀 启动 Redis..."
            "$RAILS_ROOT/bin/redis" start
            ;;
        sidekiq)
            echo "🚀 启动 Sidekiq..."
            "$RAILS_ROOT/bin/sidekiq" start
            ;;
        rails)
            echo "🚀 启动 Rails..."
            if [ -f "$RAILS_ROOT/tmp/pids/server.pid" ]; then
                local pid=$(cat "$RAILS_ROOT/tmp/pids/server.pid")
                if ps -p "$pid" > /dev/null 2>&1; then
                    echo "Rails 服务器已在运行，PID: $pid"
                    return 0
                else
                    rm -f "$RAILS_ROOT/tmp/pids/server.pid"
                fi
            fi
            
            cd "$RAILS_ROOT" || return 1
            # 使用 Rails 内置的守护进程模式
            rails server -d
            if [ $? -eq 0 ]; then
                local pid=$(cat tmp/pids/server.pid)
                echo "Rails 服务器已启动，PID: $pid"
                echo "日志文件: $RAILS_ROOT/log/development.log"
                echo "URL: http://localhost:3000"
            else
                echo "❌ Rails 启动失败"
                return 1
            fi
            ;;
        all)
            start_service redis
            sleep 2
            start_service sidekiq
            sleep 2
            start_service rails
            ;;
        *)
            echo "❌ 未知服务: $service"
            return 1
            ;;
    esac
}

stop_service() {
    local service=$1
    case $service in
        redis)
            echo "🛑 停止 Redis..."
            "$RAILS_ROOT/bin/redis" stop
            ;;
        sidekiq)
            echo "🛑 停止 Sidekiq..."
            "$RAILS_ROOT/bin/sidekiq" stop
            ;;
        rails)
            echo "🛑 停止 Rails..."
            if [ -f "$RAILS_ROOT/tmp/pids/server.pid" ]; then
                kill "$(cat "$RAILS_ROOT/tmp/pids/server.pid")" 2>/dev/null || true
                rm -f "$RAILS_ROOT/tmp/pids/server.pid"
                echo "Rails 服务器已停止"
            else
                echo "Rails 服务器未运行"
            fi
            ;;
        all)
            stop_service rails
            sleep 1
            stop_service sidekiq
            sleep 1
            stop_service redis
            ;;
        *)
            echo "❌ 未知服务: $service"
            return 1
            ;;
    esac
}

restart_service() {
    local service=$1
    echo "🔄 重启 $service..."
    stop_service "$service"
    sleep 2
    start_service "$service"
}

status_service() {
    local service=$1
    case $service in
        redis)
            echo "📊 Redis 状态:"
            "$RAILS_ROOT/bin/redis" status
            ;;
        sidekiq)
            echo "📊 Sidekiq 状态:"
            "$RAILS_ROOT/bin/sidekiq" status
            ;;
        rails)
            echo "📊 Rails 状态:"
            if [ -f "$RAILS_ROOT/tmp/pids/server.pid" ]; then
                local pid=$(cat "$RAILS_ROOT/tmp/pids/server.pid")
                if ps -p "$pid" > /dev/null 2>&1; then
                    echo "✅ Rails 正在运行，PID: $pid"
                    echo "URL: http://localhost:3000"
                else
                    echo "❌ Rails 未运行（PID文件存在但进程不存在）"
                    # 清理无效的PID文件
                    rm -f "$RAILS_ROOT/tmp/pids/server.pid"
                fi
            else
                echo "❌ Rails 未运行"
            fi
            ;;
        all)
            echo "=== 🔍 DeepSport 服务状态 ==="
            echo ""
            status_service redis
            echo ""
            status_service sidekiq  
            echo ""
            status_service rails
            echo ""
            echo "=== 📱 Web 访问地址 ==="
            echo "应用主页: http://localhost:3000"
            echo "Sidekiq 监控: http://localhost:3000/sidekiq"
            ;;
        *)
            echo "❌ 未知服务: $service"
            return 1
            ;;
    esac
}

# 主逻辑
cd "$RAILS_ROOT" || exit 1

ACTION=${1:-status}
SERVICE=${2:-all}

case $ACTION in
    start)
        start_service "$SERVICE"
        ;;
    stop)
        stop_service "$SERVICE"
        ;;
    restart)
        restart_service "$SERVICE"
        ;;
    status)
        status_service "$SERVICE"
        ;;
    *)
        show_usage
        exit 1
        ;;
esac