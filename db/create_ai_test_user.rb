# 创建AI聊天测试用户
# 运行命令: rails runner db/create_ai_test_user.rb

test_email = '<EMAIL>'
test_password = 'password123'

# 查找或创建测试用户
test_user = User.find_or_initialize_by(email: test_email)

if test_user.new_record?
  test_user.assign_attributes(
    name: 'AI测试用户',
    password: test_password,
    role: 'user',
    status: 'active',
    email_verified_at: Time.current
  )
  
  if test_user.save
    puts "✓ AI测试用户已创建："
    puts "  邮箱: #{test_email}"
    puts "  密码: #{test_password}"
    puts ""
    puts "现在可以使用这个账号测试AI聊天功能了！"
    puts "访问: http://localhost:3000/websocket_test.html"
  else
    puts "✗ 创建AI测试用户失败："
    puts test_user.errors.full_messages.join(", ")
  end
else
  puts "✓ AI测试用户已存在: #{test_email}"
  puts "  密码: #{test_password}"
  puts ""
  puts "可以直接使用这个账号测试AI聊天功能！"
  puts "访问: http://localhost:3000/websocket_test.html"
end