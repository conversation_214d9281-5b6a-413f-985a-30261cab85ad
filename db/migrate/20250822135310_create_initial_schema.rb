class CreateInitialSchema < ActiveRecord::Migration[8.0]
  def change
    # 用户表 - 核心用户实体，包含认证、角色管理和个人资料
    create_table :users, comment: '用户表 - 存储用户基本信息和认证数据' do |t|
      # 基本信息
      t.string :name, null: false, limit: 100, comment: '用户姓名'
      t.string :email, null: false, limit: 255, comment: '邮箱地址，用于登录和验证'
      t.string :password_digest, null: false, comment: '加密后的密码'

      # 角色和状态管理
      t.string :roles, array: true, default: ['user'], null: false, comment: "用户角色数组：支持多个角色如['user', 'admin']"
      t.integer :status, null: false, default: 1, comment: '用户状态：0-待验证，1-已激活，2-已禁用'

      # 邮箱验证
      t.boolean :email_verified, null: false, default: false, comment: '邮箱是否已验证'
      t.datetime :email_verified_at, comment: '邮箱验证时间'

      # 登录记录
      t.datetime :last_login_at, comment: '最后登录时间'
      t.string :last_login_ip, comment: '最后登录IP地址'

      # 密码重置
      t.string :password_reset_token, comment: '密码重置令牌'
      t.datetime :password_reset_sent_at, comment: '密码重置令牌发送时间'

      # 个人资料
      t.decimal :weight, precision: 5, scale: 2, comment: '体重(kg)'
      t.decimal :height, precision: 5, scale: 2, comment: '身高(cm)'
      t.date :birthday, comment: '生日'
      t.integer :gender, default: 0, comment: '性别：0-未知，1-男，2-女'

      # 问卷相关字段
      t.integer :exercise_frequency, comment: '运动频率：0-偶尔锻炼(0-2次/周)，1-定期锻炼(3-5次/周)，2-专业运动员(6+次/周)'
      t.text :fitness_goals, comment: "用户健身目标(JSON数组)"
      t.text :obstacles, comment: "阻碍用户实现目标的因素(JSON数组)"

      t.timestamps null: false, comment: 'created_at: 创建时间, updated_at: 更新时间'

      # 索引
      t.index :email, unique: true, comment: '邮箱唯一索引'
      t.index :password_reset_token, unique: true, comment: '密码重置令牌唯一索引'
      t.index :roles, using: 'gin', comment: "角色数组索引，支持高效的角色查询"
      t.index [:roles, :status], comment: "角色数组和状态组合索引，用于权限检查"
      t.index :email_verified, comment: '邮箱验证状态索引'
      t.index :last_login_at, comment: '最后登录时间索引，用于统计'
      t.index :birthday, comment: '生日索引'
      t.index :gender, comment: '性别索引'
      t.index :exercise_frequency, comment: '运动频率索引'
    end

    # 会话表 - 管理用户登录会话
    create_table :sessions, comment: '会话表 - 管理用户登录会话和API认证' do |t|
      t.references :user, null: false, foreign_key: true, comment: '关联用户ID'
      t.string :token, null: false, limit: 255, comment: '会话令牌，用于API认证'
      t.string :ip_address, comment: '登录IP地址'
      t.string :user_agent, comment: '用户代理信息'
      t.datetime :expires_at, null: false, comment: '会话过期时间'
      t.boolean :revoked, null: false, default: false, comment: '是否已撤销'
      t.datetime :revoked_at, comment: '撤销时间'
      t.timestamps null: false, comment: 'created_at: 创建时间, updated_at: 更新时间'

      # 索引
      t.index :token, unique: true, comment: '令牌唯一索引'
      t.index [:user_id, :revoked], comment: '用户活跃会话索引'
      t.index :expires_at, comment: '过期时间索引，用于清理'
      t.index [:revoked, :expires_at], comment: '撤销状态和过期时间组合索引'
    end

    # 验证码表 - 管理邮箱验证和密码重置验证码
    create_table :verification_codes, comment: '验证码表 - 管理邮箱验证和密码重置验证码' do |t|
      t.string :email, null: false, limit: 255, comment: '验证码发送的邮箱地址'
      t.string :code, null: false, limit: 10, comment: '验证码'
      t.integer :code_type, null: false, comment: '验证码类型：0-邮箱验证，1-密码重置'
      t.datetime :expires_at, null: false, comment: '验证码过期时间'
      t.boolean :used, null: false, default: false, comment: '是否已使用'
      t.datetime :used_at, comment: '使用时间'
      t.integer :attempts, null: false, default: 0, comment: '尝试次数'
      t.timestamps null: false, comment: 'created_at: 创建时间, updated_at: 更新时间'

      # 索引
      t.index [:email, :code_type, :used], comment: '邮箱、类型、使用状态组合索引'
      t.index [:code, :code_type], comment: '验证码和类型组合索引'
      t.index :expires_at, comment: '过期时间索引，用于清理'
      t.index [:email, :expires_at], comment: '邮箱和过期时间组合索引'
    end

    # 聊天室表 - 存储聊天室基本信息
    create_table :chat_rooms, comment: '聊天室表 - 存储聊天室基本信息和配置' do |t|
      t.string :name, null: false, limit: 100, comment: '聊天室名称'
      t.text :description, comment: '聊天室描述'
      t.integer :status, default: 0, null: false, comment: '聊天室状态：0-活跃，1-非活跃'
      t.references :user, null: false, foreign_key: true, comment: '聊天室创建者ID'
      t.timestamps null: false, comment: 'created_at: 创建时间, updated_at: 更新时间'

      # 索引
      t.index :status, comment: '聊天室状态索引，用于查询活跃聊天室'
      t.index :created_at, comment: '创建时间索引，用于排序'
    end

    # 聊天室参与者表 - 管理用户与聊天室的关系
    create_table :chat_room_participants, comment: '聊天室参与者表 - 管理用户与聊天室的关系' do |t|
      t.references :chat_room, null: false, foreign_key: true, comment: '关联聊天室ID'
      t.references :user, null: false, foreign_key: true, comment: '关联用户ID'
      t.integer :role, default: 0, null: false, comment: '参与者角色：0-普通成员，1-管理员'
      t.datetime :joined_at, null: false, comment: '加入聊天室的时间'
      t.datetime :left_at, comment: '离开聊天室的时间，null表示仍在聊天室中'
      t.timestamps null: false, comment: 'created_at: 创建时间, updated_at: 更新时间'

      # 索引
      t.index [:chat_room_id, :user_id], unique: true, comment: '聊天室和用户组合唯一索引'
      t.index :role, comment: '角色索引，用于权限检查'
      t.index :joined_at, comment: '加入时间索引，用于排序'
      t.index :left_at, comment: '离开时间索引，用于查询活跃用户'
    end

    # 聊天消息表 - 存储聊天室中的所有消息
    create_table :chat_messages, comment: '聊天消息表 - 存储聊天室中的用户和AI消息' do |t|
      t.references :chat_room, null: false, foreign_key: true, comment: '关联聊天室ID'
      t.references :user, null: true, foreign_key: true, comment: '用户ID，AI消息时为null'
      t.text :content, null: false, comment: '消息内容'
      t.integer :message_type, default: 0, null: false, comment: '消息类型：0-用户消息，1-AI消息，2-系统消息'
      t.integer :token_count, default: 0, comment: '消耗的token总数量'
      t.string :ai_model_name, comment: 'AI模型名称，如GPT-4、Claude等'
      t.integer :prompt_tokens, default: 0, comment: '输入prompt消耗的token数量'
      t.integer :completion_tokens, default: 0, comment: 'AI回复消耗的token数量'
      t.json :metadata, comment: '额外的元数据，如模型参数、配置等'
      t.references :parent_message, null: true, foreign_key: { to_table: :chat_messages }, comment: '父消息ID，用于回复和引用关系'
      t.integer :status, default: 0, null: false, comment: '消息状态：0-已发送，1-处理中，2-失败'
      t.timestamps null: false, comment: 'created_at: 创建时间, updated_at: 更新时间'

      # 索引
      t.index [:chat_room_id, :created_at], comment: '聊天室和创建时间组合索引，用于分页查询'
      t.index :created_at, comment: '创建时间索引，用于消息排序'
      t.index :message_type, comment: '消息类型索引，用于筛选不同类型消息'
      t.index :status, comment: '消息状态索引，用于查询处理状态'
    end

    # AI聊天会话表 - 管理AI聊天的会话信息
    create_table :ai_chat_sessions, comment: 'AI聊天会话表 - 管理AI聊天的会话信息和统计数据' do |t|
      t.references :user, null: false, foreign_key: true, comment: '关联用户ID'
      t.references :chat_room, null: false, foreign_key: true, comment: '关联聊天室ID'
      t.string :session_name, limit: 100, comment: '会话名称，用户自定义'
      t.json :model_config, comment: 'AI模型配置，包括温度、最大token等参数'
      t.integer :total_tokens, default: 0, comment: '会话总消耗token数量'
      t.integer :total_prompt_tokens, default: 0, comment: '会话总输入token数量'
      t.integer :total_completion_tokens, default: 0, comment: '会话总输出token数量'
      t.integer :status, default: 0, null: false, comment: '会话状态：0-活跃，1-已归档，2-已删除'
      t.datetime :last_message_at, comment: '最后一条消息的时间'
      t.timestamps null: false, comment: 'created_at: 创建时间, updated_at: 更新时间'

      # 索引
      t.index :status, comment: '会话状态索引，用于查询活跃会话'
      t.index :last_message_at, comment: '最后消息时间索引，用于排序'
      t.index [:user_id, :status], comment: '用户和状态组合索引，用于查询用户的活跃会话'
      t.index [:chat_room_id, :status], comment: '聊天室和状态组合索引'
      t.index :created_at, comment: '创建时间索引，用于排序'
    end
  end
end