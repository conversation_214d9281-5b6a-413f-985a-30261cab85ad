class CreateExerciseAnalyses < ActiveRecord::Migration[8.0]
  def change
    create_table :exercise_analyses, comment: '运动分析记录表 - 存储用户上传的运动数据图片和分析结果' do |t|
      t.references :user, null: false, foreign_key: true, comment: '关联用户ID'
      t.integer :status, null: false, default: 0, comment: '分析状态：0-pending(待处理), 1-analyzing(分析中), 2-completed(完成), 3-failed(失败)'
      t.json :analysis_result, comment: '分析结果(JSON格式)'
      t.timestamps null: false, comment: 'created_at: 创建时间, updated_at: 更新时间'
    end
    
    # 添加状态和创建时间的索引
    add_index :exercise_analyses, :status, comment: '状态索引，用于查询不同状态的分析记录'
    add_index :exercise_analyses, :created_at, comment: '创建时间索引，用于排序'
  end
end
