# This file should ensure the existence of records required to run the application in every environment (production,
# development, test). The code here should be idempotent so that it can be executed at any point in every environment.
#
# The data can then be loaded with the bin/rails db:seed command (or created alongside the database with db:setup).
#
# Example:
#
#   ["Action", "Comedy", "Drama", "Horror"].each do |genre_name|
#     MovieGenre.find_or_create_by!(name: genre_name)
#   end

# 创建默认管理员用户
admin_email = '<EMAIL>'
admin_password = 'admin123456'

admin = User.find_or_initialize_by(email: admin_email)

if admin.new_record?
  admin.assign_attributes(
    name: '系统管理员',
    password: admin_password,
    roles: ['admin'],
    status: 'active',
    email_verified: true,
    email_verified_at: Time.current
  )
  
  if admin.save
    puts "✓ 默认管理员已创建："
    puts "  邮箱: #{admin_email}"
    puts "  密码: #{admin_password}"
    puts "  角色: #{admin.roles.join(', ')}"
    puts "  请及时修改默认密码！"
  else
    puts "✗ 创建默认管理员失败："
    puts admin.errors.full_messages.join(", ")
  end
else
  # 如果管理员已存在但还是旧的角色格式，更新为新格式
  if admin.roles.empty?
    admin.update!(roles: ['admin'])
    puts "✓ 已更新管理员角色格式: #{admin_email}"
  else
    puts "✓ 默认管理员已存在: #{admin_email} (角色: #{admin.roles.join(', ')})"
  end
end

# 创建测试用户（仅在开发环境）
if Rails.env.development?
  test_user_email = '<EMAIL>'
  test_user_password = 'user123456'
  
  test_user = User.find_or_initialize_by(email: test_user_email)
  
  if test_user.new_record?
    test_user.assign_attributes(
      name: '测试用户',
      password: test_user_password,
      roles: ['user'],
      status: 'active',
      email_verified: true,
      email_verified_at: Time.current
    )
    
    if test_user.save
      puts "✓ 测试用户已创建："
      puts "  邮箱: #{test_user_email}"
      puts "  密码: #{test_user_password}"
      puts "  角色: #{test_user.roles.join(', ')}"
    else
      puts "✗ 创建测试用户失败："
      puts test_user.errors.full_messages.join(", ")
    end
  else
    # 如果测试用户已存在但还是旧的角色格式，更新为新格式
    if test_user.roles.empty?
      test_user.update!(roles: ['user'])
      puts "✓ 已更新测试用户角色格式: #{test_user_email}"
    else
      puts "✓ 测试用户已存在: #{test_user_email} (角色: #{test_user.roles.join(', ')})"
    end
  end
end
