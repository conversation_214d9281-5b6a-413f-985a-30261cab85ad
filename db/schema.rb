# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source <PERSON>s uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[8.0].define(version: 2025_09_06_162954) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "pg_catalog.plpgsql"

  create_table "active_storage_attachments", force: :cascade do |t|
    t.string "name", null: false
    t.string "record_type", null: false
    t.bigint "record_id", null: false
    t.bigint "blob_id", null: false
    t.datetime "created_at", null: false
    t.index ["blob_id"], name: "index_active_storage_attachments_on_blob_id"
    t.index ["record_type", "record_id", "name", "blob_id"], name: "index_active_storage_attachments_uniqueness", unique: true
  end

  create_table "active_storage_blobs", force: :cascade do |t|
    t.string "key", null: false
    t.string "filename", null: false
    t.string "content_type"
    t.text "metadata"
    t.string "service_name", null: false
    t.bigint "byte_size", null: false
    t.string "checksum"
    t.datetime "created_at", null: false
    t.index ["key"], name: "index_active_storage_blobs_on_key", unique: true
  end

  create_table "active_storage_variant_records", force: :cascade do |t|
    t.bigint "blob_id", null: false
    t.string "variation_digest", null: false
    t.index ["blob_id", "variation_digest"], name: "index_active_storage_variant_records_uniqueness", unique: true
  end

  create_table "ai_chat_sessions", comment: "AI聊天会话表 - 管理AI聊天的会话信息和统计数据", force: :cascade do |t|
    t.bigint "user_id", null: false, comment: "关联用户ID"
    t.bigint "chat_room_id", null: false, comment: "关联聊天室ID"
    t.string "session_name", limit: 100, comment: "会话名称，用户自定义"
    t.json "model_config", comment: "AI模型配置，包括温度、最大token等参数"
    t.integer "total_tokens", default: 0, comment: "会话总消耗token数量"
    t.integer "total_prompt_tokens", default: 0, comment: "会话总输入token数量"
    t.integer "total_completion_tokens", default: 0, comment: "会话总输出token数量"
    t.integer "status", default: 0, null: false, comment: "会话状态：0-活跃，1-已归档，2-已删除"
    t.datetime "last_message_at", comment: "最后一条消息的时间"
    t.datetime "created_at", null: false, comment: "created_at: 创建时间, updated_at: 更新时间"
    t.datetime "updated_at", null: false, comment: "created_at: 创建时间, updated_at: 更新时间"
    t.index ["chat_room_id", "status"], name: "index_ai_chat_sessions_on_chat_room_id_and_status", comment: "聊天室和状态组合索引"
    t.index ["chat_room_id"], name: "index_ai_chat_sessions_on_chat_room_id"
    t.index ["created_at"], name: "index_ai_chat_sessions_on_created_at", comment: "创建时间索引，用于排序"
    t.index ["last_message_at"], name: "index_ai_chat_sessions_on_last_message_at", comment: "最后消息时间索引，用于排序"
    t.index ["status"], name: "index_ai_chat_sessions_on_status", comment: "会话状态索引，用于查询活跃会话"
    t.index ["user_id", "status"], name: "index_ai_chat_sessions_on_user_id_and_status", comment: "用户和状态组合索引，用于查询用户的活跃会话"
    t.index ["user_id"], name: "index_ai_chat_sessions_on_user_id"
  end

  create_table "chat_messages", comment: "聊天消息表 - 存储聊天室中的用户和AI消息", force: :cascade do |t|
    t.bigint "chat_room_id", null: false, comment: "关联聊天室ID"
    t.bigint "user_id", comment: "用户ID，AI消息时为null"
    t.text "content", null: false, comment: "消息内容"
    t.integer "message_type", default: 0, null: false, comment: "消息类型：0-用户消息，1-AI消息，2-系统消息"
    t.integer "token_count", default: 0, comment: "消耗的token总数量"
    t.string "ai_model_name", comment: "AI模型名称，如GPT-4、Claude等"
    t.integer "prompt_tokens", default: 0, comment: "输入prompt消耗的token数量"
    t.integer "completion_tokens", default: 0, comment: "AI回复消耗的token数量"
    t.json "metadata", comment: "额外的元数据，如模型参数、配置等"
    t.bigint "parent_message_id", comment: "父消息ID，用于回复和引用关系"
    t.integer "status", default: 0, null: false, comment: "消息状态：0-已发送，1-处理中，2-失败"
    t.datetime "created_at", null: false, comment: "created_at: 创建时间, updated_at: 更新时间"
    t.datetime "updated_at", null: false, comment: "created_at: 创建时间, updated_at: 更新时间"
    t.index ["chat_room_id", "created_at"], name: "index_chat_messages_on_chat_room_id_and_created_at", comment: "聊天室和创建时间组合索引，用于分页查询"
    t.index ["chat_room_id"], name: "index_chat_messages_on_chat_room_id"
    t.index ["created_at"], name: "index_chat_messages_on_created_at", comment: "创建时间索引，用于消息排序"
    t.index ["message_type"], name: "index_chat_messages_on_message_type", comment: "消息类型索引，用于筛选不同类型消息"
    t.index ["parent_message_id"], name: "index_chat_messages_on_parent_message_id"
    t.index ["status"], name: "index_chat_messages_on_status", comment: "消息状态索引，用于查询处理状态"
    t.index ["user_id"], name: "index_chat_messages_on_user_id"
  end

  create_table "chat_room_participants", comment: "聊天室参与者表 - 管理用户与聊天室的关系", force: :cascade do |t|
    t.bigint "chat_room_id", null: false, comment: "关联聊天室ID"
    t.bigint "user_id", null: false, comment: "关联用户ID"
    t.integer "role", default: 0, null: false, comment: "参与者角色：0-普通成员，1-管理员"
    t.datetime "joined_at", null: false, comment: "加入聊天室的时间"
    t.datetime "left_at", comment: "离开聊天室的时间，null表示仍在聊天室中"
    t.datetime "created_at", null: false, comment: "created_at: 创建时间, updated_at: 更新时间"
    t.datetime "updated_at", null: false, comment: "created_at: 创建时间, updated_at: 更新时间"
    t.index ["chat_room_id", "user_id"], name: "index_chat_room_participants_on_chat_room_id_and_user_id", unique: true, comment: "聊天室和用户组合唯一索引"
    t.index ["chat_room_id"], name: "index_chat_room_participants_on_chat_room_id"
    t.index ["joined_at"], name: "index_chat_room_participants_on_joined_at", comment: "加入时间索引，用于排序"
    t.index ["left_at"], name: "index_chat_room_participants_on_left_at", comment: "离开时间索引，用于查询活跃用户"
    t.index ["role"], name: "index_chat_room_participants_on_role", comment: "角色索引，用于权限检查"
    t.index ["user_id"], name: "index_chat_room_participants_on_user_id"
  end

  create_table "chat_rooms", comment: "聊天室表 - 存储聊天室基本信息和配置", force: :cascade do |t|
    t.string "name", limit: 100, null: false, comment: "聊天室名称"
    t.text "description", comment: "聊天室描述"
    t.integer "status", default: 0, null: false, comment: "聊天室状态：0-活跃，1-非活跃"
    t.bigint "user_id", null: false, comment: "聊天室创建者ID"
    t.datetime "created_at", null: false, comment: "created_at: 创建时间, updated_at: 更新时间"
    t.datetime "updated_at", null: false, comment: "created_at: 创建时间, updated_at: 更新时间"
    t.index ["created_at"], name: "index_chat_rooms_on_created_at", comment: "创建时间索引，用于排序"
    t.index ["status"], name: "index_chat_rooms_on_status", comment: "聊天室状态索引，用于查询活跃聊天室"
    t.index ["user_id"], name: "index_chat_rooms_on_user_id"
  end

  create_table "exercise_analyses", comment: "运动分析记录表 - 存储用户上传的运动数据图片和分析结果", force: :cascade do |t|
    t.bigint "user_id", null: false, comment: "关联用户ID"
    t.integer "status", default: 0, null: false, comment: "分析状态：0-pending(待处理), 1-analyzing(分析中), 2-completed(完成), 3-failed(失败)"
    t.json "analysis_result", comment: "分析结果(JSON格式)"
    t.datetime "created_at", null: false, comment: "created_at: 创建时间, updated_at: 更新时间"
    t.datetime "updated_at", null: false, comment: "created_at: 创建时间, updated_at: 更新时间"
    t.index ["created_at"], name: "index_exercise_analyses_on_created_at", comment: "创建时间索引，用于排序"
    t.index ["status"], name: "index_exercise_analyses_on_status", comment: "状态索引，用于查询不同状态的分析记录"
    t.index ["user_id"], name: "index_exercise_analyses_on_user_id"
  end

  create_table "sessions", comment: "会话表 - 管理用户登录会话和API认证", force: :cascade do |t|
    t.bigint "user_id", null: false, comment: "关联用户ID"
    t.string "token", limit: 255, null: false, comment: "会话令牌，用于API认证"
    t.string "ip_address", comment: "登录IP地址"
    t.string "user_agent", comment: "用户代理信息"
    t.datetime "expires_at", null: false, comment: "会话过期时间"
    t.boolean "revoked", default: false, null: false, comment: "是否已撤销"
    t.datetime "revoked_at", comment: "撤销时间"
    t.datetime "created_at", null: false, comment: "created_at: 创建时间, updated_at: 更新时间"
    t.datetime "updated_at", null: false, comment: "created_at: 创建时间, updated_at: 更新时间"
    t.index ["expires_at"], name: "index_sessions_on_expires_at", comment: "过期时间索引，用于清理"
    t.index ["revoked", "expires_at"], name: "index_sessions_on_revoked_and_expires_at", comment: "撤销状态和过期时间组合索引"
    t.index ["token"], name: "index_sessions_on_token", unique: true, comment: "令牌唯一索引"
    t.index ["user_id", "revoked"], name: "index_sessions_on_user_id_and_revoked", comment: "用户活跃会话索引"
    t.index ["user_id"], name: "index_sessions_on_user_id"
  end

  create_table "users", comment: "用户表 - 存储用户基本信息和认证数据", force: :cascade do |t|
    t.string "name", limit: 100, null: false, comment: "用户姓名"
    t.string "email", limit: 255, null: false, comment: "邮箱地址，用于登录和验证"
    t.string "password_digest", null: false, comment: "加密后的密码"
    t.string "roles", default: ["user"], null: false, comment: "用户角色数组：支持多个角色如['user', 'admin']", array: true
    t.integer "status", default: 1, null: false, comment: "用户状态：0-待验证，1-已激活，2-已禁用"
    t.boolean "email_verified", default: false, null: false, comment: "邮箱是否已验证"
    t.datetime "email_verified_at", comment: "邮箱验证时间"
    t.datetime "last_login_at", comment: "最后登录时间"
    t.string "last_login_ip", comment: "最后登录IP地址"
    t.string "password_reset_token", comment: "密码重置令牌"
    t.datetime "password_reset_sent_at", comment: "密码重置令牌发送时间"
    t.decimal "weight", precision: 5, scale: 2, comment: "体重(kg)"
    t.decimal "height", precision: 5, scale: 2, comment: "身高(cm)"
    t.date "birthday", comment: "生日"
    t.integer "gender", default: 0, comment: "性别：0-未知，1-男，2-女"
    t.integer "exercise_frequency", comment: "运动频率：0-偶尔锻炼(0-2次/周)，1-定期锻炼(3-5次/周)，2-专业运动员(6+次/周)"
    t.text "fitness_goals", comment: "用户健身目标(JSON数组)"
    t.text "obstacles", comment: "阻碍用户实现目标的因素(JSON数组)"
    t.datetime "created_at", null: false, comment: "created_at: 创建时间, updated_at: 更新时间"
    t.datetime "updated_at", null: false, comment: "created_at: 创建时间, updated_at: 更新时间"
    t.index ["birthday"], name: "index_users_on_birthday", comment: "生日索引"
    t.index ["email"], name: "index_users_on_email", unique: true, comment: "邮箱唯一索引"
    t.index ["email_verified"], name: "index_users_on_email_verified", comment: "邮箱验证状态索引"
    t.index ["exercise_frequency"], name: "index_users_on_exercise_frequency", comment: "运动频率索引"
    t.index ["gender"], name: "index_users_on_gender", comment: "性别索引"
    t.index ["last_login_at"], name: "index_users_on_last_login_at", comment: "最后登录时间索引，用于统计"
    t.index ["password_reset_token"], name: "index_users_on_password_reset_token", unique: true, comment: "密码重置令牌唯一索引"
    t.index ["roles", "status"], name: "index_users_on_roles_and_status", comment: "角色数组和状态组合索引，用于权限检查"
    t.index ["roles"], name: "index_users_on_roles", using: :gin, comment: "角色数组索引，支持高效的角色查询"
  end

  create_table "verification_codes", comment: "验证码表 - 管理邮箱验证和密码重置验证码", force: :cascade do |t|
    t.string "email", limit: 255, null: false, comment: "验证码发送的邮箱地址"
    t.string "code", limit: 10, null: false, comment: "验证码"
    t.integer "code_type", null: false, comment: "验证码类型：0-邮箱验证，1-密码重置"
    t.datetime "expires_at", null: false, comment: "验证码过期时间"
    t.boolean "used", default: false, null: false, comment: "是否已使用"
    t.datetime "used_at", comment: "使用时间"
    t.integer "attempts", default: 0, null: false, comment: "尝试次数"
    t.datetime "created_at", null: false, comment: "created_at: 创建时间, updated_at: 更新时间"
    t.datetime "updated_at", null: false, comment: "created_at: 创建时间, updated_at: 更新时间"
    t.index ["code", "code_type"], name: "index_verification_codes_on_code_and_code_type", comment: "验证码和类型组合索引"
    t.index ["email", "code_type", "used"], name: "index_verification_codes_on_email_and_code_type_and_used", comment: "邮箱、类型、使用状态组合索引"
    t.index ["email", "expires_at"], name: "index_verification_codes_on_email_and_expires_at", comment: "邮箱和过期时间组合索引"
    t.index ["expires_at"], name: "index_verification_codes_on_expires_at", comment: "过期时间索引，用于清理"
  end

  add_foreign_key "active_storage_attachments", "active_storage_blobs", column: "blob_id"
  add_foreign_key "active_storage_variant_records", "active_storage_blobs", column: "blob_id"
  add_foreign_key "ai_chat_sessions", "chat_rooms"
  add_foreign_key "ai_chat_sessions", "users"
  add_foreign_key "chat_messages", "chat_messages", column: "parent_message_id"
  add_foreign_key "chat_messages", "chat_rooms"
  add_foreign_key "chat_messages", "users"
  add_foreign_key "chat_room_participants", "chat_rooms"
  add_foreign_key "chat_room_participants", "users"
  add_foreign_key "chat_rooms", "users"
  add_foreign_key "exercise_analyses", "users"
  add_foreign_key "sessions", "users"
end
